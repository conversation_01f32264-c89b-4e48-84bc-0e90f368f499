﻿using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Model.Requests.PayTr;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Controllers.v1.PayTr;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController]
[AllowAnonymous]
public class PayTrController : BaseController
{
    private readonly IMediator _mediatr;

    public PayTrController(IMediator mediatr)
    {
        _mediatr = mediatr;
    }


    /// <summary>
    /// Ödeme işlemi sonrası PayTR nin bize dönüş yapacağı URL dir
    /// </summary>
    /// <returns></returns>
    [HttpPost("Success")]
    public async Task Success([FromForm] PaymentReturnRequest request)
    {
        var result = await _mediatr.Send(request);

        if (result.Success)
        {

            await Response.WriteAsync("OK");
        }
        else
        {
            await Response.WriteAsync("PAYTR notification failed: bad hash");
        }
    }
}
