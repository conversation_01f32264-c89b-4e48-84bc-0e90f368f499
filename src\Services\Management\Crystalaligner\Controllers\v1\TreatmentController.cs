﻿using Crystalaligner.Application.Commands.Treatment;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class TreatmentController : BaseController
    {
        private readonly IMediator _mediatr;
        public TreatmentController(IMediator mediatr) => _mediatr = mediatr;



        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Treatment, bool>> predicate = s => s.Id > 0;

            var query = new TreatmentsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            Expression<Func<Treatment, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new TreatmentsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<Treatment, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new TreatmentQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] TreatmentUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
