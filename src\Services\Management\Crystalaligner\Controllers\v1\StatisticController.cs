﻿using Crystalaligner.Application.Commands.Statistics;
using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class StatisticController : BaseController
    {
        private readonly IMediator _mediatr;

        public StatisticController(IMediator mediatr)
        {
            _mediatr = mediatr;
        }

        [HttpGet("GetByLanguageId")]
        public async Task<IActionResult> GetByLangugageId([FromQuery] StatisticGetByLanguageIdQuery query)
        {
            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] StatisticCreate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult>  WhoWeAreFeaturePut([FromBody] StatisticUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

    }
}
