using Crystalaligner.Core.Base.Entities;
using Crystalaligner.Core.Base.Helpers.Audits;
using Crystalaligner.Core.Base.Helpers.GenericExpressions;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Base.Responses;
using Crystalaligner.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using System.Linq.Expressions;

namespace Crystalaligner.Core.Base.Repositories
{
    public class Repository<T> : IRepository<T> where T : EntityBase
    {
        private readonly BaseContext Context;

        public Repository(BaseContext context)
        {
            Context = context;
        }
        public async Task<T> AddAsync(T entity)
        {
            using var transaction = Context.Database.BeginTransaction();

            try
            {
                Context.Set<T>().Add(entity);

                await Context.SaveChangesAsync();

                transaction.Commit();

                return entity;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return null;
            }
        }
        public async Task<int> AddAsync(ICollection<T> entity)
        {
            using var transaction = Context.Database.BeginTransaction();
            int count = 0;
            try
            {
                foreach (T item in entity)
                {
                    Context.Set<T>().Add(item);
                    count++;
                }

                await Context.SaveChangesAsync();

                transaction.Commit();

                return count;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return 0;
            }
        }
        public async Task<AuditResponse<T>> UpdateAsync(T entity)
        {
            using var transaction = Context.Database.BeginTransaction();

            try
            {

                #region Audits

                var current = Context.Entry(entity).CurrentValues.Clone();

                Context.Entry(entity).Reload();

                var OriginalValues = Context.Entry(entity).CurrentValues.Clone().ToObject();

                Context.Entry(entity).CurrentValues.SetValues(current);

                #endregion

                Context.Entry(entity).State = EntityState.Modified;
                foreach (var item in entity.GetType().GetProperties())
                {
                    string test = item.Name;

                    if (item is IEntityBase)
                    {
                        Context.Entry(item).State = EntityState.Modified;
                    }

                }
                await Context.SaveChangesAsync();

                transaction.Commit();

                var audit = OriginalValues.Compare<T>(entity);

                return new AuditResponse<T>(true, audit);

            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new AuditResponse<T>(false);
            }
        }
        public async Task<AuditResponse<T>> SaveAllAsync(T entity) => await Context.SaveAllAsync(entity);
        public async Task<bool> DeleteAsync(T entity)
        {
            using var transaction = Context.Database.BeginTransaction();

            try
            {
                entity.GetType().GetProperty("IsDelete").SetValue(entity, true);
                Context.Set<T>().Update(entity);
                Context.Entry(entity).State = EntityState.Modified;
                await Context.SaveChangesAsync();

                transaction.Commit();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return false;
            }

        }
        public async Task<bool> RemoveAsync(T entity)
        {
            using var transaction = Context.Database.BeginTransaction();

            try
            {
                Context.Set<T>().Remove(entity);
                Context.Entry(entity).State = EntityState.Deleted;
                await Context.SaveChangesAsync();

                transaction.Commit();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return false;
            }
        }
        public async Task<IEnumerable<T>> GetAllAsync(
            Expression<Func<T, bool>> predicate = null,
            IList<string> includes = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            int? TopCount = null,
            string QueryTag = null,
            bool disableTracking = true)
        {
            IQueryable<T> query = Context.Set<T>();

            if (disableTracking) query = query.AsNoTracking();

            if (includes != null)
                query = includes.Aggregate(query, (current, include) => current.Include(include));

            if (predicate != null) query = query.Where(predicate);

            if (TopCount != null) query = query.Take(TopCount.Value);

            if (QueryTag != null) query = query.TagWith(QueryTag);

            if (orderBy != null)
            {
                string sql1 = orderBy(query).ToQueryString();
                return await orderBy(query).ToListAsync();
            }
            string sql = query.ToQueryString();

            return await query.ToListAsync();
        }
        public async Task<T> GetAsyncWithThenInclude(
            Expression<Func<T, bool>> predicate = null,
            IList<string> includes = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string QueryTag = null,
            bool disableTracking = true,
            Func<IQueryable<T>, IIncludableQueryable<T, object>> thenInclude = null)
        {
            IQueryable<T> query = Context.Set<T>();

            if (disableTracking) query = query.AsNoTracking();

            if (includes != null)
                query = includes.Aggregate(query, (current, include) => current.Include(include));

            if (predicate != null) query = query.Where(predicate);

            if (QueryTag != null) query = query.TagWith(QueryTag);

            if (thenInclude != null)
                query = thenInclude(query);

            if (orderBy != null)
                return await orderBy(query).FirstOrDefaultAsync();

            return await query.FirstOrDefaultAsync();
        }

        public async Task<T> GetAsync(
            Expression<Func<T, bool>> predicate = null,
            IList<string> includes = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string QueryTag = null,
            bool disableTracking = true)
        {
            IQueryable<T> query = Context.Set<T>();

            if (disableTracking) query = query.AsNoTracking();

            if (includes != null)
                query = includes.Aggregate(query, (current, include) => current.Include(include));

            if (predicate != null) query = query.Where(predicate);

            if (QueryTag != null) query = query.TagWith(QueryTag);

            if (orderBy != null)
                return await orderBy(query).FirstOrDefaultAsync();
            string sql = query.ToQueryString();
            return await query.FirstOrDefaultAsync();
        }

        public virtual async Task<IEnumerable<R>> GetGroupedAsync<R>(GenericGroupExpression<T, R> parameters)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete).AsNoTracking();

            if (parameters.includePaths != null)
                query = parameters.includePaths(query);

            if (parameters.predicate != null) query = query.Where(parameters.predicate);

            var grouped = query.GroupBy(parameters.groupBy);

            if (parameters.orderBy != null) grouped = parameters.orderBy(grouped);

            var result = grouped.Select(parameters.selector);

            if (parameters.topCount.HasValue)
                result = result.Take(parameters.topCount.Value);

            string sql = result.ToQueryString();

            return await result.ToListAsync();
        }
        public async Task<PagedModel<T>> GetAllPagedAsync(
            Expression<Func<T, bool>> predicate,
            int PageNumber,
            int PageSize,
            string QueryTag = null,
            IList<string> includes = null)
        {
            IQueryable<T> query = Context.Set<T>();

            if (includes != null)
                query = includes.Aggregate(query, (current, include) => current.Include(include));

            if (predicate != null) query = query.Where(predicate);

            if (QueryTag != null) query = query.TagWith(QueryTag);

            var totalRecords = await query.CountAsync();

            var pagedData = await query
                 .Skip((PageNumber - 1) * PageSize).Take(PageSize)
                 .ToListAsync();

            return new PagedModel<T>(pagedData, totalRecords);
        }

        public async Task<AuditResponse<T>> UpdateAsync(T entity, Expression<Func<T, object>>[] includeProperties)
        {
            using var transaction = Context.Database.BeginTransaction();

            try
            {
                var dbEntry = Context.Entry(entity);
                foreach (var includeProperty in includeProperties)
                {
                    dbEntry.Property(includeProperty).IsModified = true;
                }

                await Context.SaveChangesAsync();

                transaction.Commit();
                return new AuditResponse<T>(true);
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new AuditResponse<T>(false);
            }
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync(GenericExpression<T> parameters = null)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete);

            if (parameters == null) return query;

            if (parameters.disableTracking)
                query = query.AsNoTracking();

            if (parameters.includePaths != null)
                query = parameters.includePaths(query);

            if (parameters.predicate != null)
                query = query.Where(parameters.predicate);

            if (parameters.selector != null)
                query = query.Select(parameters.selector);

            if (parameters.orderBy != null)
                return await parameters.orderBy(query).ToListAsync();

            string sql = query.ToQueryString();

            return await query.ToListAsync();
        }
        public virtual async Task<int> GetCountAsync(GenericExpression<T> parameters = null)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete);

            if (parameters == null)
                return 0;

            if (parameters.disableTracking)
                query = query.AsNoTracking();

            if (parameters.includePaths != null)
                query = parameters.includePaths(query);

            if (parameters.predicate != null)
                query = query.Where(parameters.predicate);

            string sql = query.ToQueryString();

            return await query.CountAsync();
        }

        public virtual async Task<T> GetAsync(GenericExpression<T> parameters = null)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete);

            if (parameters == null)
                return await query.FirstOrDefaultAsync();

            if (parameters.disableTracking)
                query = query.AsNoTracking();

            if (parameters.includePaths != null)
                query = parameters.includePaths(query);

            if (parameters.predicate != null)
                query = query.Where(parameters.predicate);

            if (parameters.selector != null)
                query = query.Select(parameters.selector);

            if (parameters.orderBy != null)
                return await parameters.orderBy(query).FirstOrDefaultAsync();

            string sql = query.ToQueryString();
            return await query.FirstOrDefaultAsync();
        }
        public virtual async Task<PagedModel<T>> GetAllPagedAsyncV1(PagedFilterRequestV1<T> filterRequest)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete);

            if (filterRequest.IncludePaths != null)
                query = filterRequest.IncludePaths(query);

            if (filterRequest.Predicate != null) query = query.Where(filterRequest.Predicate);

            query = filterRequest.OrderBy == null ? query.OrderByDescending(a => a.Id) : filterRequest.OrderBy(query);

            var totalRecords = await query.CountAsync();

            var pageNumber = filterRequest.FilterQuery.PageNumber;
            var pageSize = filterRequest.FilterQuery.PageSize;

            var pagedData = await query
                 .Skip((pageNumber - 1) * pageSize).Take(pageSize)
                 .ToListAsync();

            string sql = query.ToQueryString();

            return new PagedModel<T>(pagedData, totalRecords);
        }

        public virtual async Task<PagedModel<T>> GetAllPagedAsync(PagedFilterRequest<T> filterRequest)
        {
            IQueryable<T> query = Context.Set<T>().Where(a => !a.IsDelete);

            if (filterRequest.IncludePaths != null)
                query = filterRequest.IncludePaths(query);

            if (filterRequest.Predicate != null) query = query.Where(filterRequest.Predicate);

            query = filterRequest.OrderBy == null ? query.OrderByDescending(a => a.Id) : filterRequest.OrderBy(query);

            var totalRecords = await query.CountAsync();

            var pageNumber = filterRequest.FilterQuery.PageNumber;
            var pageSize = filterRequest.FilterQuery.PageSize;

            var pagedData = await query
                 .Skip((pageNumber - 1) * pageSize).Take(pageSize)
                 .ToListAsync();

            string sql = query.ToQueryString();

            return new PagedModel<T>(pagedData, totalRecords);
        }

    }
}
