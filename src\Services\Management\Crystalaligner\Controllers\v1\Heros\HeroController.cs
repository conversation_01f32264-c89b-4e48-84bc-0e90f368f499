﻿using Crystalaligner.Application.Commands.Heros;
using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities.Heros;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1.Heros
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class HeroController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;
        public HeroController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = Array.Empty<string>();

            Expression<Func<Hero, bool>> predicate = s => s.Id > 0;

            var query = new HeroSelectQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
       
        [HttpPut]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image,
            [FromForm] int Id,
            [FromForm] string ContactLink,
            [FromForm] string VideoLink)
        {
            var command = new HeroUpdate();
            command.Id = Id; 
            command.ContactLink = ContactLink;
            command.VideoLink = VideoLink;
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/images"))
                    client.CreateDirectory("Resources/images");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/images/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/images/{newFileName}");
                    }

                    command.BrochureLink = "https://cdn.crystalaligner.com/" + $"Resources/images/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception ex)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
