﻿using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Model.Requests;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class NotificationController : BaseController
    {
        private readonly IMediator _mediatr;

        public NotificationController(IMediator mediatr)
        {
            _mediatr = mediatr;
        }

        [HttpPost("FCMRegister")]
        public async Task<IActionResult> FCMRegister([FromBody] FCMRegister command)
        {
            var result = await _mediatr.Send(command); 

            if (result == null) return NotFound(result);

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPost("SendNotification")]
        public async Task<IActionResult> SendNotification([FromBody] SendNotification command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound(result);

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpPost("SendNotificationAllUser")]
        public async Task<IActionResult> SendNotificationAllUser([FromBody] SendNotificationAllUser command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound(result);

            return result.Success ? Success(result) : Accepted(result);
        }
    }
}
