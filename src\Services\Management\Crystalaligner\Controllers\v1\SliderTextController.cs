﻿using Crystalaligner.Application.Commands.SliderTexts;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Domain.Entities.Heros;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class SliderTextController : BaseController
    {
        private readonly IMediator _mediatr;
        public SliderTextController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<SliderText, bool>> predicate = s => s.Id > 0;

            var query = new SliderTextsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}"), AllowAnonymous]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<SliderText, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new SliderTextsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

      

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<SliderText, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new SliderTextQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] SliderTextCreate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] SliderTextUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            Expression<Func<SliderText, bool>> predicate = s => s.Id == id;

            var query = new SliderTextDelete(predicate);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
