﻿using Crystalaligner.Application.Commands.Users;
using Crystalaligner.Application.Queries.SingleQuery.Users;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.EmailHelper;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Management.Domain.Entities.Users;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v2.Users;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiController]
[ApiVersion("2.0")]
[BasicAuthentication]
public class UsersController : BaseController
{
    private readonly IMediator _mediatr;

    public static IConfiguration _configuration { get; set; }
    public UsersController(IConfiguration configuration, IMediator mediatr)
    {
        _configuration = configuration;
        _mediatr = mediatr;
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] UserCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("SendEmail")]
    public async Task<IActionResult> SendEmail([FromBody] EmailModel emailModel)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var mailInfo = _configuration.GetSection("SmtpHelper").Get<EmailSenderModel>();

        if (mailInfo is not null)
        {
            var smtpClientHelper = new SmtpClientHelper(mailInfo.Host, mailInfo.Port, mailInfo.Mail, mailInfo.Password, false);
            try
            {
                await smtpClientHelper.SendEmailAsync(
                    mailInfo.Mail,
                    emailModel.ToEmail,
                    emailModel.Subject,
                    emailModel.Body
                );

                return Ok("E-posta başarıyla gönderildi.");
            }
            catch (Exception ex)
            {
                return BadRequest("E-posta gönderimi sırasında bir hata oluştu: " + ex.Message);
            }
        }
        else
            return BadRequest("E-posta gönderimi sırasında bir hata oluştu: Mail bilgileri ayarlanmamış.");
    }

    [HttpPost("ForgotPassword/{email}")]
    public async Task<IActionResult> ForgotPassword(string email)
    {
        Expression<Func<User, bool>> predicate = s => s.Email == email;

        var includes = Array.Empty<string>();

        var query = new ForgotPasswordQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("ResetPassword")]
    public async Task<IActionResult> ResetPassword([FromBody] PasswordReset command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("CheckPasswordGuid/{guid}"), AllowAnonymous]
    public async Task<IActionResult> CheckPasswordGuid(string guid)
    {
        Expression<Func<User, bool>> predicate = s => guid != Guid.Empty.ToString() && s.Guid.ToString() == guid && s.GuidExpirationDate > DateTime.Now;

        var includes = Array.Empty<string>();

        var query = new CheckPasswordGuidQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpPost("Activation/{guid}"), AllowAnonymous]
    public async Task<IActionResult> Activation(string guid)
    {
        Expression<Func<User, bool>> predicate = s => guid != Guid.Empty.ToString() && s.Code.ToString() == guid.ToUpper() && s.IsActive == false;

        var includes = Array.Empty<string>();

        var query = new ActivationQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
