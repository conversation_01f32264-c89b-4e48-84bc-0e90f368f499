﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Controllers.v1.PayTr
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    public class PayController : ControllerBase
    {
        private readonly IMediator _mediatr;

        public PayController(IMediator mediatr)
        {
            _mediatr = mediatr;
        }
   
        [HttpPost("Success")]
        public async Task Success([FromForm] Crystalaligner.Model.Requests.PayTr.PaymentReturnRequest request)
        {
            var result = await _mediatr.Send(request);

            if (result.Success)
            {
                await Response.WriteAsync("OK");
            }
            else
            {
                await Response.WriteAsync("PAYTR notification failed: bad hash");
            }
        }
    }
}
