﻿using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class HowItWorkController : BaseController
    {
        private readonly IMediator _mediatr;
        public HowItWorkController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> Get(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<HowItWork, bool>> predicate = s => s.Id > 0 && s.LanguageId == LanguageId;

            var query = new Application.Queries.MultipleQuery.HowItWorks.HowItWorksQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
