﻿using Crystalaligner.Application.Commands.Chats;
using Crystalaligner.Application.Queries.MultipleQuery.Chats;
using Crystalaligner.Application.Queries.SingleQuery.Chats;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Chats;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Chats;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class ChatMessagesController : BaseController
{
    private readonly IMediator _mediatr;
    public ChatMessagesController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<ChatMessage>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new ChatMessagesPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Chat_Mesaj_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<ChatMessage, bool>> predicate = s => s.Id > 0;

        var query = new ChatMessagesQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<ChatMessage>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new ChatMessagesPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<ChatMessage, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new ChatMessageQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] ChatMessageCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] ChatMessageUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<ChatMessage, bool>> predicate = s => s.Id == id;

        var query = new ChatMessageDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
