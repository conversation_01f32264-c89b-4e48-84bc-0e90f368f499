﻿using Crystalaligner.Application.Queries.SingleQuery.FirstCases;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class FirstCaseController : BaseController
    {
        private readonly IMediator _mediatr;
        public FirstCaseController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> Get(int LanguageId)
        {
            Expression<Func<FirstCase, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new FirstCaseQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }



    }
}
