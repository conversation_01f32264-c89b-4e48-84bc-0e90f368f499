﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Packages;
using Crystalaligner.Application.Queries.MultipleQuery.Users;
using Crystalaligner.Application.Queries.SingleQuery.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.EmailHelper;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Orders;
using Crystalaligner.Management.Domain.Entities.Users;
using Crystalaligner.Model.Responses.Orders;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using static Crystalaligner.Helper.Enumerations;

namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class OrdersController : BaseController
{
    public static IConfiguration _configuration { get; set; }
    private readonly IMediator _mediatr;
    public OrdersController(IMediator mediatr, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _configuration = configuration;
    }

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Order>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));

        request.IncludePaths = includes => includes.Include(a => a.Patient);

        var packageTypeQuery = new PackageTypesQuery();
        var resultpackageType = await _mediatr.Send(packageTypeQuery);

        Expression<Func<UserAddress, bool>> predicate = s => s.UserId > 0;
        var includes = new string[] { "City", "District" };
        var userAdressQuery = new UserAddressesQuery(predicate, includes);
        var resultuserAdress = await _mediatr.Send(userAdressQuery);


        var query = new OrdersPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        List<OrderExcelResponse> response = new List<OrderExcelResponse>();
        foreach (var item in result.Data)
        {
            OrderExcelResponse order = new OrderExcelResponse();

            var adres = resultuserAdress.Data.Where(a => a.UserId == item.CreatedId).FirstOrDefault();

            order.Durum = item.Status.ToString();
            order.Hasta = item.Patient.Name;
            order.Paket = resultpackageType.Data.Where(s => s.Id == item.PackageTypeId).FirstOrDefault()?.Name;
            order.KayitTarihi = item.CreatedDate.Value.ToString("dd.MM.yyy");
            order.DoktorAdi = item.CreatedBy;
            order.DoktorAdres = adres?.Address + " " + adres?.District.Name + " / " + adres?.City.Name;
            response.Add(order);
        }
        var excelFile = CreateExcelFile(response);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Vaka_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<Order, bool>> predicate = s => s.Id > 0;

        var query = new OrdersQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<Order, bool>> predicate = s => s.Id == id;

        var includes = new string[] { "Shipping" };

        var query = new OrderQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByDoctorId")]
    public async Task<IActionResult> GetByDoctorId([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Order>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
            request.IncludePaths = p => p.Include(x => x.Patient);
        }
        else
            return BadRequest();
        var query = new OrdersPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);

    }

    [HttpGet("GetByAgentId")]
    public async Task<IActionResult> GetByAgentId([FromQuery] PaginationFilterQueryV1 filterQuery)
    {
        var request = new PagedFilterRequestV1<Order>(
                        filterQuery: filterQuery,
                        predicate: entity => entity.User.AgentId == Convert.ToInt32(filterQuery.Field),
                        includePaths: query => query.Include(entity => entity.User),
                        orderBy: query => query.OrderByDescending(entity => entity.CreatedDate)
        );

        var query = new OrdersPagedByAgentIdQuery(request, Convert.ToInt32(filterQuery.Field));
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Order>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
            request.IncludePaths = p => p.Include(x => x.Patient);
        }
        request.IncludePaths = p => p.Include(x => x.Shipping);
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new OrdersPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPagedByUserPackageId")]
    public async Task<IActionResult> GetPagedByUserPackageId([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<OrderUserPackageJunction>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        var query = new OrderUserPackageJunctionsPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("Post")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(
        IFormFile fileFaceFront,
        IFormFile fileFaceRight,
        IFormFile fileFaceSmile,
        IFormFile fileTeethFront,
        IFormFile fileTeethRight,
        IFormFile fileTeethLeft,
        IFormFile fileTeethTop,
        IFormFile fileTeethDown,
        IFormFile filePanoramic,
        IFormFile fileStlUpper,
        IFormFile fileStlLower,
        IFormFile fileStlBite,
        [FromForm] int patientId,
        [FromForm] string description,
        [FromForm] int status,
        [FromForm] int ark,
        [FromForm] int antero,
        [FromForm] int packageTypeId,
        [FromForm] int implementedPlanTypeId,
        [FromForm] string specialInstruction,
        [FromForm] string diestema,
        [FromForm] string extraction)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.OrdersUrl;

        var command = new OrderCreate();

        List<IFormFile> files = new List<IFormFile>()
        {
            fileFaceFront,
            fileFaceRight,
            fileFaceSmile,
            fileTeethFront,
            fileTeethRight,
            fileTeethLeft,
            fileTeethTop,
            fileTeethDown,
            filePanoramic,
            fileStlUpper,
            fileStlLower,
            fileStlBite
        };

        try
        {
            var client = new FtpClient(ftpServer, username, password, 21);
            client.Connect();

            if (!client.DirectoryExists(baseUrl[1..] + patientId))
                client.CreateDirectory(baseUrl + patientId);

            foreach (var file in files)
            {
                if (file is not null && file.Length > 0)
                {
                    var newName = file.Name.Replace("file", "");
                    string newFileName = patientId.ToString() + "-" + newName + Path.GetExtension(file.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        file.CopyTo(stream);
                    }


                    if (!client.FileExists($"{baseUrl}/{patientId}/{file.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"{baseUrl}/{patientId}/{newFileName}");
                    }

                    // remove file to filename
                    string propertyName = file.Name.Replace("file", "").Replace(" ", "");

                    // find matched prop.
                    var property = command.GetType().GetProperty(propertyName);
                    property?.SetValue(command, $"{baseUrl[1..]}{patientId}/{newFileName}");
                }
            }

            client.Disconnect();
        }
        catch (Exception)
        {

        }


        command.Description = description;
        command.Status = (OrderStatus)status;
        command.PatientId = patientId;
        command.Ark = (Ark)ark;
        command.Antero = (Antero)antero;
        command.PackageTypeId = packageTypeId;
        command.ImplementedPlanTypeId = implementedPlanTypeId;
        command.SpecialInstruction = specialInstruction;
        command.Diestema = diestema;
        command.Extraction = extraction;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("ImageUpdate")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> ImageUpdate(
      IFormFile file,
      [FromForm] int OrderId,
      [FromForm] int patientId,
      [FromForm] string ImageName)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.OrdersUrl;

        var command = new OrderImageUpdate();

        try
        {
            var client = new FtpClient(ftpServer, username, password, 21);
            client.Connect();

            if (!client.DirectoryExists(baseUrl[1..] + patientId))
                client.CreateDirectory(baseUrl + patientId);


            if (file is not null && file.Length > 0)
            {
                var newName = file.Name.Replace("file", "");
                string newFileName = patientId.ToString() + "-" + newName + Path.GetExtension(file.FileName);

                string tempFilePath = Path.GetTempFileName();

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                if (client.FileExists($"{baseUrl}/{patientId}/{file.FileName}"))
                {
                    client.DeleteFile($"{baseUrl}/{patientId}/{file.FileName}");
                }
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}/{patientId}/{newFileName}");

                command.ImageUrl = $"{baseUrl}/{patientId}/{newFileName}";
            }


            client.Disconnect();
        }
        catch (Exception)
        {

        }


        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpPut]
    public async Task<IActionResult> Put([FromBody] OrderUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpPut("OrderPlanUpdate")]
    public async Task<IActionResult> OrderPlanUpdate([FromBody] OrderPlanUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpPatch]
    public async Task<IActionResult> Patch(int id, [FromBody] JsonPatchDocument<Order> JsonPatchDocument)
    {
        var command = new OrderPatch(id, JsonPatchDocument);

        var result = (ApiResponse)await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("PutOrderStatus")]
    public async Task<IActionResult> PutOrderStatus([FromBody] OrderStatusUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound(); 

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("PutOrderShipped")]
    public async Task<IActionResult> PutOrderStatusToShipper([FromBody] OrderShippedUpdate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        if (result.Success)
        {
            var mailInfo = _configuration.GetSection("SmtpHelper").Get<EmailSenderModel>();

            if (mailInfo is not null)
            {
                var smtpClientHelper = new SmtpClientHelper(mailInfo.Host, mailInfo.Port, mailInfo.Mail, mailInfo.Password, false);
                try
                {
                    _ = smtpClientHelper.SendEmailAsync(
                        mailInfo.Mail,
                        result.Data.DoctorEmail,
                        "Crystal Aligner Bilgilendirme",
                        "Vakanız " + result.Data.Shipping.Name + " firması ve " + result.Data.TrackNumber + " takip numarası ile " + result.Data.ShippedDate.Value.ToShortDateString() + " tarihinde kargolanmıştır."
                    );
                }
                catch
                {
                }
            }
            return Success(result);
        }
        return BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<Order, bool>> predicate = s => s.Id == id;

        var query = new OrderDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

