﻿using Crystalaligner.Application.Commands.Testimonials;
using Crystalaligner.Application.Queries.MultipleQuery.Testimonials;
using Crystalaligner.Application.Queries.SingleQuery.Testimonials;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using DocumentFormat.OpenXml.Wordprocessing;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class TestimonialController : BaseController
    {
        private readonly IMediator _mediatr;
        public TestimonialController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Testimonial, bool>> predicate = s => s.LangugageId == LanguageId;

            var query = new TestimonialsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
