﻿using Crystalaligner.Application.Commands.Advantages;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class AdvantageController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;
        public AdvantageController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Advantage, bool>> predicate = s => s.Id > 0;

            var query = new AdvantagesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("GetByLanguageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            Expression<Func<Advantage, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new AdvantagesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<Advantage, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new AdvantageQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }


        [HttpPost("Post")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Post( [FromForm] int LanguageId, [FromForm] string Content)
        {
            AdvantageCreate command = new AdvantageCreate();
            command.LanguageId = LanguageId;
            command.Content = Content; 

      
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("Put")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image, [FromForm] int Id, [FromForm] int LanguageId, [FromForm] string Content)
        {
            AdvantageUpdate command = new AdvantageUpdate();
            command.LanguageId = LanguageId;
            command.Content = Content;
            command.Id = Id;
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/Advantage"))
                    client.CreateDirectory("Resources/Advantage");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/Advantage/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/Advantage/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/Advantage/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            Expression<Func<Advantage, bool>> predicate = s => s.Id == id;

            var query = new AdvantageDelete(predicate);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }


        [HttpGet("AdvantageImage")]
        public async Task<IActionResult> GetAdvantageImage()
        {
            var includes = new string[] { };

            Expression<Func<AdvantageImage, bool>> predicate = s => s.Id > 0;

            var query = new AdvantageImagesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("AdvantageImage")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> AdvantageImage(IFormFile image, [FromForm] int Id)
        {
            AdvantageImageUpdate command = new AdvantageImageUpdate(); 
            command.Id = Id;
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/Image"))
                    client.CreateDirectory("Resources/Image");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/Image/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/Image/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/Image/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
