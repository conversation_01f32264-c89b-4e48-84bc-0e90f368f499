﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.SingleQuery.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Orders;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;


namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class TreatmentPlanAttachmentsController : BaseController
{
    private readonly IMediator _mediatr;
    public static IConfiguration _configuration { get; set; }
    public TreatmentPlanAttachmentsController(IMediator mediatr,IConfiguration configuration) 
    { 
        _mediatr = mediatr; 
        _configuration = configuration;
    }

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<TreatmentPlanAttachment>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new TreatmentPlanAttachmentsPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Tedavi_Ekleri_Listesi.xlsx");
    }

    [HttpPost("Post")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(IFormFile file, [FromForm] int treatmentPlanId, [FromForm] string title)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.TreatmentPlanAttachmentsUrl;
        string fileToUpload = file.FileName;

        if (file.Length > 0)
        {
            var client = new FtpClient(ftpServer, username, password, 21);
            client.Connect();
            string tempFilePath = Path.GetTempFileName();

            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                file.CopyTo(stream);
            }

            if (!client.DirectoryExists(baseUrl[1..] + treatmentPlanId))
                client.CreateDirectory(baseUrl + treatmentPlanId);

            if (!client.FileExists($"/Resources/{treatmentPlanId}/{fileToUpload}"))
            {
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}{treatmentPlanId}/{fileToUpload}");
                client.Disconnect();
            }
        }

        var command = new TreatmentPlanAttachmentCreate()
        {
            Title = title,
            TreatmentPlanId = treatmentPlanId,
            Url = $"{baseUrl}/{treatmentPlanId}/{fileToUpload}",
            Extension = Path.GetExtension(file.FileName).ToString()[1..]
        };

        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }


    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<TreatmentPlanAttachment, bool>> predicate = s => s.Id > 0;

        var query = new TreatmentPlanAttachmentsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<TreatmentPlanAttachment, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new TreatmentPlanAttachmentQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<TreatmentPlanAttachment>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new TreatmentPlanAttachmentsPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] TreatmentPlanAttachmentUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<TreatmentPlanAttachment, bool>> predicate = s => s.Id == id;

        var query = new TreatmentPlanAttachmentDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

