﻿using Crystalaligner.Application.Commands.Products;
using Crystalaligner.Application.Queries.MultipleQuery.Products;
using Crystalaligner.Application.Queries.SingleQuery.Products;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Products;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Chats;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class ProductsController : BaseController
{
    public static IConfiguration _configuration { get; set; }
    private readonly IMediator _mediatr;
    public ProductsController(IMediator mediatr, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _configuration = configuration;
    }

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Product>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete && x.Id != 33);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderBy(s => s.SortOrder));
        var query = new ProductsPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Urun_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { "ProductImages" };

        Expression<Func<Product, bool>> predicate = s => s.Id > 0 && s.Id != 33; //partiaş payment product excluded

        var query = new ProductsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Product>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete && x.Id != 33);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderBy(s => s.SortOrder));

        request.IncludePaths = p => p.Include(x => x.ProductImages);

        var query = new ProductsPagedQuery(request);
        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<Product, bool>> predicate = s => s.Id == id;

        var includes = new string[] { "ProductImages" };

        var query = new ProductQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(
       IFormFile fileThumbnail,
       [FromForm] int categoryId,
       [FromForm] string name,
       [FromForm] string description,
       [FromForm] int quantity,
       [FromForm] decimal price,
       [FromForm] int taxRate)
    {
        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.ProductsUrl;

        var command = new ProductCreate();
        var client = new FtpClient(ftpServer, username, password, 21);
        client.Connect();

        if (!client.DirectoryExists(baseUrl[1..]))
            client.CreateDirectory(baseUrl);

        if (fileThumbnail is not null && fileThumbnail.Length > 0)
        {
            var newName = fileThumbnail.Name.Replace("file", "");
            string newFileName = newName + DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetExtension(fileThumbnail.FileName) ;

            string tempFilePath = Path.GetTempFileName();

            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                fileThumbnail.CopyTo(stream);
            }

            if (!client.FileExists($"{baseUrl}/{fileThumbnail.FileName}"))
            {
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}/{newFileName}");
            }

            //// remove file to filename
            //string propertyName = fileThumbnail.Name.Replace("file", "").Replace(" ", "") + DateTime.Now.ToString("yyyyMMddHHmmss");

            //// find matched prop.
            //var property = command.GetType().GetProperty(propertyName);
            //property?.SetValue(command, $"{baseUrl[1..]}/{newFileName}");
            command.Thumbnail = $"{baseUrl[1..]}/{newFileName}";
        }

        client.Disconnect();
        command.Description = description;
        command.TaxRate = taxRate;
        command.Price = price;
        command.CategoryId = categoryId;
        command.Name = name;
        command.Quantity = quantity;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put(
       IFormFile fileThumbnail,
       [FromForm] int categoryId,
       [FromForm] string name,
       [FromForm] string description,
       [FromForm] int quantity,
       [FromForm] decimal price,
       [FromForm] int taxRate,
       [FromForm] int id )
    {
        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.ProductsUrl;

        var command = new ProductUpdate();
        command.Id = id;
        var client = new FtpClient(ftpServer, username, password, 21);
        client.Connect();

        if (!client.DirectoryExists(baseUrl[1..]))
            client.CreateDirectory(baseUrl);

        if (fileThumbnail is not null && fileThumbnail.Length > 0)
        {
            var newName = fileThumbnail.Name.Replace("file", "");
            string newFileName = newName + DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetExtension(fileThumbnail.FileName);

            string tempFilePath = Path.GetTempFileName();

            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                fileThumbnail.CopyTo(stream);
            }

            if (!client.FileExists($"{baseUrl}/{fileThumbnail.FileName}"))
            {
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}/{newFileName}");
            }

            //// remove file to filename
            //string propertyName = fileThumbnail.Name.Replace("file", "").Replace(" ", "") + DateTime.Now.ToString("yyyyMMddHHmmss");

            //// find matched prop.
            //var property = command.GetType().GetProperty(propertyName);
            //property?.SetValue(command, $"{baseUrl[1..]}/{newFileName}");
            command.Thumbnail = $"{baseUrl[1..]}/{newFileName}";
        }

        client.Disconnect();
        command.Description = description;
        command.TaxRate = taxRate;
        command.Price = price;
        command.CategoryId = categoryId;
        command.Name = name;
        command.Quantity = quantity;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<Product, bool>> predicate = s => s.Id == id && s.Id != 33;

        var query = new ProductDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
