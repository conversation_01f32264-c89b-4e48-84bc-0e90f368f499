﻿using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class HowItWorkContentController : BaseController
    {
        private readonly IMediator _mediatr;
        public HowItWorkContentController(IMediator mediatr) => _mediatr = mediatr;

        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> Get(int LanguageId)
        {
            Expression<Func<HowItWorkContent, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new Application.Queries.SingleQuery.HowItWorks.HowItWorkContentQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
