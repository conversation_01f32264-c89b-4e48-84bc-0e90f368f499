﻿using Crystalaligner.Application.Commands.WhoAreWeFeatures;
using Crystalaligner.Application.Commands.WhoWeAres;
using Crystalaligner.Application.Queries.MultipleQuery.WhoWeAres;
using Crystalaligner.Application.Queries.SingleQuery.WhoWeAres;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Tools;
using DocumentFormat.OpenXml.Wordprocessing;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class WhoWeAreController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;

        public WhoWeAreController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<WhoWeAre, bool>> predicate = s => s.Id > 0;

            var query = new WhoWeAresQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            Expression<Func<WhoWeAre, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new WhoWeAreQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<WhoWeAre, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new WhoWeAreQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("Put")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image, [FromForm] int Id, [FromForm] int LanguageId, [FromForm] string Content)
        {
            WhoWeAreUpdate command = new WhoWeAreUpdate();

            command.Id = Id;
            command.LanguageId = LanguageId;
            command.Content = Content;

            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/WhoWeAre"))
                    client.CreateDirectory("Resources/WhoWeAre");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/WhoWeAre/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/WhoWeAre/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/WhoWeAre/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }



        [HttpPost("WhoWeAreFeaturePost")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult>  WhoWeAreFeaturePost(IFormFile image
            , [FromForm] int LangugageId
            , [FromForm] string Title
            , [FromForm] string Description)
        {
            WhoWeAreFeatureCreate command = new WhoWeAreFeatureCreate();
            command.LanguageId = LangugageId;
            command.Description = Description;
            command.Title = Title;

            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/WhoWeAreFeature"))
                    client.CreateDirectory("Resources/WhoWeAreFeature");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/WhoWeAreFeature/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/WhoWeAreFeature/{newFileName}");
                    }

                    command.Icon = "https://cdn.crystalaligner.com/" + $"Resources/WhoWeAreFeature/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("WhoWeAreFeaturePut")]
        public async Task<IActionResult>  WhoWeAreFeaturePut([FromBody]  WhoWeAreFeatureUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpDelete("FirstCaseFeatureDelete/{id}")]
        public async Task<IActionResult>  WhoWeAreFeatureDelete(int id)
        {
            Expression<Func<WhoWeAreFeature, bool>> predicate = s => s.Id == id;

            var query = new WhoWeAreFeatureDelete(predicate);
            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
