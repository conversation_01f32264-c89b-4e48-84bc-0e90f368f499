﻿using Crystalaligner.Application.Commands.Users;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Management.Controllers.v2.Users;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiController]
[ApiVersion("2.0")]
[BasicAuthentication]
public class UserAddressesController : BaseController
{
    private readonly IMediator _mediatr;
    public UserAddressesController(IMediator mediatr) => _mediatr = mediatr;

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] UserAddressCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }    
}
