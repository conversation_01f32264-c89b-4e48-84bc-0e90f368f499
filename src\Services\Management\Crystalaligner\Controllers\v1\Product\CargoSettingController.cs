﻿using Crystalaligner.Application.Commands.Products;
using Crystalaligner.Application.Queries.SingleQuery.Products;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities.Products;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1.Product
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class CargoSettingController : BaseController
    {
        public static IConfiguration _configuration { get; set; }
        private readonly IMediator _mediatr;
        public CargoSettingController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<CargoSetting, bool>> predicate = s => s.Id > 0;

            var query = new CargoSettingQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] CargoSettingUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
