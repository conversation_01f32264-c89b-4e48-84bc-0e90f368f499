﻿using Crystalaligner.Application.Commands.Galleries;
using Crystalaligner.Application.Queries.MultipleQuery.Galleries;
using Crystalaligner.Application.Queries.SingleQuery.Galleries;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Domain.Entities;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class GalleryController : BaseController
    {
        private readonly IConfiguration _configuration;
        private readonly IMediator _mediatr;
        public GalleryController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Gallery, bool>> predicate = s => s.Id > 0;

            var query = new GalleriesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<Gallery, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new GalleryQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPost]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Post(IFormFile image)
        {
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            GalleryCreate command = new GalleryCreate();
            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/images"))
                    client.CreateDirectory("Resources/images");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/images/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/images/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/images/{newFileName}"; 
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] GalleryUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            Expression<Func<Gallery, bool>> predicate = s => s.Id == id;

            var query = new GalleryDelete(predicate);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
