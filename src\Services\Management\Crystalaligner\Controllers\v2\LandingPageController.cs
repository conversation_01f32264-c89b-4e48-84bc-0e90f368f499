﻿using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Application.Queries.MultipleQuery.FAQs;
using Crystalaligner.Application.Queries.MultipleQuery.Questions;
using Crystalaligner.Application.Queries.MultipleQuery.Testimonials;
using Crystalaligner.Application.Queries.SingleQuery.FirstCases;
using Crystalaligner.Application.Queries.SingleQuery.WhoWeAres;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class LandingPageController : BaseController
    {
        private readonly IMediator _mediatr;
        public LandingPageController(IMediator mediatr) => _mediatr = mediatr;


        [HttpGet("GetAboutUsByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetAboutUsByLangugageId(int LanguageId)
        {
            var includes = Array.Empty<string>();

            Expression<Func<AboutUs, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new AboutUsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetFaqByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetFaqByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Faq, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new FAQsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetFirstCaseByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetFirstCaseByLangugageId(int LanguageId)
        {
            Expression<Func<FirstCase, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new FirstCaseQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetHowItWorkContentByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetHowItWorkContentByLangugageId(int LanguageId)
        {
            Expression<Func<HowItWorkContent, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new Application.Queries.SingleQuery.HowItWorks.HowItWorkContentQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetHowItWorkByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetHowItWorkByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<HowItWork, bool>> predicate = s => s.Id > 0 && s.LanguageId == LanguageId;

            var query = new Application.Queries.MultipleQuery.HowItWorks.HowItWorksQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetQuestionByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetQuestionByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Question, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new QuestionsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetTestimonialByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetTestimonialByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Testimonial, bool>> predicate = s => s.LangugageId == LanguageId;

            var query = new TestimonialsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetWhoWeAreByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetWhoWeAreByLangugageId(int LanguageId)
        {
            Expression<Func<WhoWeAre, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new WhoWeAreQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
