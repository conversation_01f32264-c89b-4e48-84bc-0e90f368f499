﻿using Crystalaligner.Application.Commands.Questions;
using Crystalaligner.Application.Queries.MultipleQuery.Questions;
using Crystalaligner.Application.Queries.SingleQuery.Questions;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Model.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class QuestionController : BaseController
    {
        private readonly IMediator _mediatr;
        public QuestionController(IMediator mediatr) => _mediatr = mediatr; 
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Question, bool>> predicate = s => s.Id > 0;

            var query = new QuestionsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        } 
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<Question, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new QuestionQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Question, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new QuestionsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            //ApiResponse<QuestionResponse> data = new ApiResponse<QuestionResponse>();
            //data.Success = true;
            //data.Data = result.Data.FirstOrDefault();
            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] QuestionCreate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] QuestionUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            Expression<Func<Question, bool>> predicate = s => s.Id == id;

            var query = new QuestionDelete(predicate);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
