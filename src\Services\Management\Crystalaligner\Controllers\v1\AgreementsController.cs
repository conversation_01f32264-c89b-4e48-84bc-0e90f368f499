﻿using Crystalaligner.Application.Commands.Agreements;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class AgreementsController : BaseController
{
    private readonly IMediator _mediatr;
    public AgreementsController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = Array.Empty<string>();

        Expression<Func<Agreement, bool>> predicate = s => s.Id > 0;

        var query = new AgreementsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] AgreementUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }    
}
