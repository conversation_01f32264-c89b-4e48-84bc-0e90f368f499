﻿using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class StatisticController : BaseController
    {
        private readonly IMediator _mediatr;

        public StatisticController(IMediator mediatr)
        {
            _mediatr = mediatr;
        }

        [HttpGet("GetByLanguageId")]
        public async Task<IActionResult> GetByLangugageId([FromQuery] StatisticGetByLanguageIdQuery query)
        {
            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
