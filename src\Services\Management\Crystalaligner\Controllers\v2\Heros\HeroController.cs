﻿using Crystalaligner.Application.Queries.SingleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities.Heros;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2.Heros
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class HeroController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;
        public HeroController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = Array.Empty<string>();

            Expression<Func<Hero, bool>> predicate = s => s.Id > 0;

            var query = new HeroSelectQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
