﻿using Crystalaligner.Application.Commands.AboutUs;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Management.Domain.Entities;
using Crystalaligner.Model.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class AboutUsController : BaseController
{
    private readonly IMediator _mediatr;
    public AboutUsController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = Array.Empty<string>();

        Expression<Func<AboutUs, bool>> predicate = s => s.Id > 0;

        var query = new AboutUsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpGet("GetByLanguageId/{LanguageId}")]
    public async Task<IActionResult> GetByLanguageId(int LanguageId)
    {
        var includes = Array.Empty<string>();

        Expression<Func<AboutUs, bool>> predicate = s => s.LanguageId == LanguageId;

        var query = new AboutUsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        ApiResponse<AboutUsResponse> data = new ApiResponse<AboutUsResponse>();
        data.Success = true;
        data.Data = result.Data.FirstOrDefault();
        return result.Success ? Success(data) : BadRequest(data);
    }
    [HttpPut]
    public async Task<IActionResult> Put([FromBody] AboutUsUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }    
}
