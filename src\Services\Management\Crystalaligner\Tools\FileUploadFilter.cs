﻿using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

public class FileUploadFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var formParameters = context.ApiDescription.ParameterDescriptions
            .Where(paramDesc => paramDesc.IsFromForm());
        if (formParameters.Count() > 0 && formParameters.FirstOrDefault().Name == "file")
        {
            if (operation.RequestBody == null)
                operation.RequestBody = new OpenApiRequestBody();

            var consumes = operation.Parameters.Any(p => p.In.ToString().ToLower() == "body")
                ? operation.Parameters.Where(p => p.In.ToString().ToLower() == "body").ToList()
                : operation.Parameters.Where(p => p.In.ToString().ToLower() == "query").ToList();

            foreach (var consume in consumes)
                operation.Parameters.Remove(consume);

            var uploadFileMediaType = new OpenApiMediaType()
            {
                Schema = new OpenApiSchema()
                {
                    Type = "object",
                    Properties =
                        {
                            ["files"] = new OpenApiSchema()
                            {
                                Type = "array",
                                Items = new OpenApiSchema()
                                {
                                    Type = "string",
                                    Format = "binary"
                                }
                            }
                        },
                    Required = new HashSet<string>() { "files" }
                }
            };

            if (!operation.RequestBody.Content.ContainsKey("multipart/form-data"))
                operation.RequestBody.Content.Add("multipart/form-data", uploadFileMediaType);
        }
    }
}

public static class Helper
{
    internal static bool IsFromForm(this ApiParameterDescription apiParameter)
    {
        if (apiParameter.Name.ToLower().Contains("file"))
        {
            var source = apiParameter.Source;
            var elementType = apiParameter.ModelMetadata?.ElementType;

            return (source == BindingSource.Form || source == BindingSource.FormFile)
                || (elementType != null && typeof(IFormFile).IsAssignableFrom(elementType));
        }
        return false;
    }
}