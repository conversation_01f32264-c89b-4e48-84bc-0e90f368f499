﻿using Crystalaligner.Application.Commands.Packages;
using Crystalaligner.Application.Queries.MultipleQuery.Packages;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities.Packages;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1.Product
{

    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class PriceListController : BaseController
    {
        public static IConfiguration _configuration { get; set; }
        private readonly IMediator _mediatr;
        public PriceListController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }


        [HttpGet]
        public async Task<IActionResult> Get(string Currency)
        {
            var includes = new string[] { };

            Expression<Func<PriceList, bool>> predicate = s => s.Currency == Currency;

            var query = new PriceListsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(
                               IFormFile fileThumbnail,
                               [FromForm] string Currency,
                               [FromForm] string Content,
                               [FromForm] int Id)
        {

            PriceListUpdate command = new PriceListUpdate();


            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;
            string baseUrl = "/Resources/Uploads/Files/";

            var client = new FtpClient(ftpServer, username, password, 21);
            if (!client.DirectoryExists(baseUrl[1..]))
                client.CreateDirectory(baseUrl);
            if (fileThumbnail is not null && fileThumbnail.Length > 0)
            {
                var newName = fileThumbnail.Name.Replace("file", "");
                string newFileName = newName + Path.GetExtension(fileThumbnail.FileName);
                if (Currency == "TR")
                {
                    newFileName = newFileName.Replace("Thumbnail", "fiyat_listesi_"+DateTime.Now.ToString("yyyyMMddHHmmss"));
                }
                else
                {
                    newFileName =""+ newFileName.Replace("Thumbnail", "price_list_" + DateTime.Now.ToString("yyyyMMddHHmmss"));
                }
                string tempFilePath = Path.GetTempFileName();

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    fileThumbnail.CopyTo(stream);
                }

               // if (!client.FileExists($"{baseUrl}/{fileThumbnail.FileName}"))
                {
                    using var fileStream = System.IO.File.OpenRead(tempFilePath);
                    client.UploadFile(tempFilePath, $"{baseUrl}/{newFileName}");
                }

                command.Url = "https://cdn.crystalaligner.com/" + $"{baseUrl[1..]}/{newFileName}";
            }

            command.Currency = Currency;
            command.Content = Content;
            command.Id = Id;

            client.Disconnect();
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
