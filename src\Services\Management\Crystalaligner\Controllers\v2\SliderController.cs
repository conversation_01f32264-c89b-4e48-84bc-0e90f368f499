﻿using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities.Heros;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class SliderController : BaseController
    {
        private readonly IMediator _mediatr;
        public SliderController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Slider, bool>> predicate = s => s.Id > 0;

            var query = new SlidersQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
