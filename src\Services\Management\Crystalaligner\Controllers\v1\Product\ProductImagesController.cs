﻿using Crystalaligner.Application.Commands.Products;
using Crystalaligner.Application.Queries.MultipleQuery.Products;
using Crystalaligner.Application.Queries.SingleQuery.Products;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Products;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Chats;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class ProductImagesController : BaseController
{
    private readonly IMediator _mediatr;
    public readonly IConfiguration _configuration;
    public ProductImagesController(IMediator mediatr, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _configuration = configuration;
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<ProductImage, bool>> predicate = s => s.Id > 0;

        var query = new ProductImagesQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<ProductImage>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new ProductImagesPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<ProductImage, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new ProductImageQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(IFormFile Image
        , [FromForm] int ProductId
        , [FromForm] int SortOrder)
    {
        ProductImageCreate command = new ProductImageCreate();
        command.ProductId = ProductId;
        //command.Image = Image;
        command.SortOrder = 1;
       var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.ProductsUrl;

        var client = new FtpClient(ftpServer, username, password, 21);
        client.Connect();

        if (!client.DirectoryExists(baseUrl[1..]))
            client.CreateDirectory(baseUrl);

        if (Image is not null && Image.Length > 0)
        {
            var newName = Image.Name.Replace("file", "");
            string newFileName = newName + DateTime.Now.ToString("yyyyMMddHHmmss") + Path.GetExtension(Image.FileName);

            string tempFilePath = Path.GetTempFileName();

            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                Image.CopyTo(stream);
            }

            if (!client.FileExists($"{baseUrl}/{Image.FileName}"))
            {
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}/{newFileName}");
            }

 
            command.Image = $"{baseUrl[1..]}/{newFileName}";
        }

        client.Disconnect();

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] ProductImageUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<ProductImage, bool>> predicate = s => s.Id == id;

        var query = new ProductImageDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
