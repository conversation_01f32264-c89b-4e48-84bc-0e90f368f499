﻿using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Management.Tools;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class WhatsAppController : BaseController
    {
        private readonly IConfiguration _configuration;

        public WhatsAppController(IConfiguration configuration)
            => _configuration = configuration;

        [HttpPost("SendMessage")]
        public async Task<IActionResult> SendMessage([FromBody] WhatsAppMessageRequest request)
        {
            try
            {
                var phoneNumberId = _configuration["WhatsAppSettings:PhoneNumberId"];
                var accessToken = _configuration["WhatsAppSettings:AccessToken"];
                var baseUrl = _configuration["WhatsAppSettings:BaseUrl"];

                var apiUrl = $"{baseUrl}{phoneNumberId}/messages";

                string jsonRequest = JsonSerializer.Serialize(request);

                using (var client = new HttpClient())
                {
                    var stringContent = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    var response = await client.PostAsync(apiUrl, stringContent);
                    string resultJson = response.Content.ReadAsStringAsync().Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var result = JsonSerializer.Deserialize<WhatsAppSendMessageResponse>(resultJson);
                        SuccessApiResponse<WhatsAppSendMessageResponse> successApiResponse = new(result, "İşlem Başarılı");
                        return Success(successApiResponse);
                    }
                    else
                    {
                        var result = JsonSerializer.Deserialize<WhatsAppSendMessageErrorResponse>(resultJson);
                        ErrorApiResponse<WhatsAppSendMessageErrorResponse> errorApiResponse = new(result, "İşlem Başarısız");
                        return Accepted(errorApiResponse);
                    }
                }
            }
            catch (Exception)
            {
                return Accepted();
            }

        }
    }

    public class WhatsAppMessageRequest
    {
        public string messaging_product { get; set; }
        public string recipient_type { get; set; }
        public string to { get; set; }
        public string type { get; set; }
        public WhatsAppMessageTemplateRequest template { get; set; }
    }
    public class WhatsAppMessageTemplateRequest
    {
        public string name { get; set; }
        public TemplateLanguageRequest language { get; set; }
        public List<TemplateComponentRequest> components { get; set; }

        public class TemplateLanguageRequest
        {
            public string code { get; set; }
        }

        public class TemplateComponentRequest
        {
            public string type { get; set; }
            public List<ComponentParameterRequest> parameters { get; set; }

            public class ComponentParameterRequest
            {
                public string type { get; set; }
                public string text { get; set; }
            }
        }
    }


    public class WhatsAppSendMessageResponse
    {
        public string messaging_product { get; set; }
        public List<WhatsAppSendMessageContact> contacts { get; set; }
        public List<WhatsAppSendMessageMessageId> messages { get; set; }
    }
    public class WhatsAppSendMessageContact
    {
        public string input { get; set; }
        public string wa_id { get; set; }
    }
    public class WhatsAppSendMessageMessageId
    {
        public string id { get; set; }
    }



    public class WhatsAppSendMessageErrorResponse
    {
        public WhatsAppSendMessageError error { get; set; }
    }
    public class WhatsAppSendMessageError
    {
        public string message { get; set; }
        public string type { get; set; }
        public int code { get; set; }
        public ErrorData error_data { get; set; }
        public string fbtrace_id { get; set; }
    }
    public class ErrorData
    {
        public string messaging_product { get; set; }
        public string details { get; set; }
    }
}
