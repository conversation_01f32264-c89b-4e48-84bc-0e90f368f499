﻿using Crystalaligner.Application.Commands.Users;
using Crystalaligner.Application.Queries.MultipleQuery.Users;
using Crystalaligner.Application.Queries.SingleQuery.Users;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Users;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class UserInvoiceAddressesController : BaseController
{
    private readonly IMediator _mediatr;
    public UserInvoiceAddressesController(IMediator mediatr) => _mediatr = mediatr;


    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<UserInvoiceAddress, bool>> predicate = s => s.Id > 0;

        var query = new UserInvoiceAddressQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<UserInvoiceAddress, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new UserInvoiceAddressQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByUserId/{id}")]
    public async Task<IActionResult> GetByUserId(int id)
    {
        Expression<Func<UserInvoiceAddress, bool>> predicate = s => s.UserId == id;

        var includes = new string[] { };

        var query = new UserInvoiceAddressesQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] UserInvoiceAddressCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] UserInvoiceAddressUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<UserInvoiceAddress, bool>> predicate = s => s.Id == id;

        var query = new UserInvoiceAddressDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
