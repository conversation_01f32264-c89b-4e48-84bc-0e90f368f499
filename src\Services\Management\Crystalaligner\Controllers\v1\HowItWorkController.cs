﻿using Crystalaligner.Application.Commands.HowItWork;
using Crystalaligner.Application.Queries.MultipleQuery.HowItWorks;
using Crystalaligner.Application.Queries.SingleQuery.HowItWorks;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Model.Responses;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class HowItWorkController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;
        public HowItWorkController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<HowItWork, bool>> predicate = s => s.Id > 0;

            var query = new Application.Queries.MultipleQuery.HowItWorks.HowItWorksQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<HowItWork, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new Application.Queries.SingleQuery.HowItWorks.HowItWorkQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<HowItWork, bool>> predicate = s => s.Id > 0 && s.LanguageId == LanguageId;

            var query = new HowItWorksQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            //ApiResponse<HowItWorkResponse> data = new ApiResponse<HowItWorkResponse>();
            //data.Success = true;
            //data.Data = result.Data.FirstOrDefault();
            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("Put")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image
            , [FromForm] int Id
            , [FromForm] int LanguageId
            , [FromForm] string Body)
        {
            HowItWorkUpdate command = new HowItWorkUpdate();
            command.Id = Id;
            command.Body = Body;
            command.LanguageId = LanguageId;
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/HowItWork"))
                    client.CreateDirectory("Resources/HowItWork");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/HowItWork/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/HowItWork/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/HowItWork/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
 
    }
}
