﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.SingleQuery.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities.Orders;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;


namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class RefinementTreatmentPlanAttachmentsController : BaseController
{
    private readonly IMediator _mediatr;
    public static IConfiguration _configuration { get; set; }
    public RefinementTreatmentPlanAttachmentsController(IMediator mediatr,IConfiguration configuration) 
    { 
        _mediatr = mediatr; 
        _configuration = configuration;
    }

   

    [HttpPost("Post")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(IFormFile file, [FromForm] int refinementTreatmentPlanId, [FromForm] string title)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.RefinementTreatmentPlanAttachmentsUrl;
        string fileToUpload = file.FileName;

        if (file.Length > 0)
        {
            var client = new FtpClient(ftpServer, username, password, 21);
            client.Connect();
            string tempFilePath = Path.GetTempFileName();

            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                file.CopyTo(stream);
            }

            if (!client.DirectoryExists(baseUrl[1..] + refinementTreatmentPlanId))
                client.CreateDirectory(baseUrl + refinementTreatmentPlanId);

            if (!client.FileExists($"/Resources/{refinementTreatmentPlanId}/{fileToUpload}"))
            {
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}{refinementTreatmentPlanId}/{fileToUpload}");
                client.Disconnect();
            }
        }

        var command = new RefinementTreatmentPlanAttachmentCreate()
        {
            Title = title,
            RefinementTreatmentPlanId = refinementTreatmentPlanId,
            Url = $"{baseUrl}/{refinementTreatmentPlanId}/{fileToUpload}",
            Extension = Path.GetExtension(file.FileName).ToString()[1..]
        };

        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }


    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<RefinementTreatmentPlanAttachment, bool>> predicate = s => s.Id > 0;

        var query = new RefinementTreatmentPlanAttachmentsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<RefinementTreatmentPlanAttachment, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new RefinementTreatmentPlanAttachmentQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] RefinementTreatmentPlanAttachmentUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<RefinementTreatmentPlanAttachment, bool>> predicate = s => s.Id == id;

        var query = new RefinementTreatmentPlanAttachmentDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

