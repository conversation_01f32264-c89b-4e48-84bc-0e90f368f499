﻿using Crystalaligner.Application.Commands.Questions;
using Crystalaligner.Application.Queries.MultipleQuery.Questions;
using Crystalaligner.Application.Queries.SingleQuery.Questions;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class QuestionController : BaseController
    {
        private readonly IMediator _mediatr;
        public QuestionController(IMediator mediatr) => _mediatr = mediatr;

        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Question, bool>> predicate = s => s.LanguageId == LanguageId;

            var query = new QuestionsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        } 
        
    }
}
