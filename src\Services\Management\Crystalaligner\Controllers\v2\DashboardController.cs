﻿using Crystalaligner.Application.Queries.SingleQuery.Dashboards;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.ViewModels.Dashboards;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    public class DashboardController : BaseController
    {
        private readonly IMediator _mediatr;
        public DashboardController(IMediator mediatr) => _mediatr = mediatr;

        [HttpGet("GetMobileHomePage")]
        public async Task<IActionResult> GetMobileHomePage(int? status, string? name)
        {
            var includes = new string[] { };

            Expression<Func<MobileHomePage, bool>> predicate = s => s.OrderId > 0;


            var query = new MobileHomePageQuery(predicate, includes, status, name);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("GetOrderStatusStatistics")]
        public async Task<IActionResult> GetOrderStatusStatistics()
        {
            var includes = new string[] { };

            //Expression<Func<Domain.ViewModels.Dashboards.OrderStatusStatistic, bool>> predicate = s => s.OrderId > 0;

            var query = new OrderStatusStatisticQuery(null, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }


        [HttpGet("CountDoctorOrders")]
        public async Task<IActionResult> CountDoctorOrders()
        {
            var query = new CountDoctorOrdersQuery();

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("CountOfDoctorsByCity")]
        public async Task<IActionResult> CountOfDoctorsByCity()
        {
            var query = new CountOfDoctorsByCityQuery();

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("CountOfOrdersByStatus")]
        public async Task<IActionResult> CountOfOrdersByStatus()
        {
            var query = new CountOfOrdersByStatusQuery();

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("CountAvailableQuantityByPackageType")]
        public async Task<IActionResult> CountAvailableQuantityByPackageType()
        {
            var query = new CountAvailableQuantityByPackageTypeQuery();

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("Summary")]
        public async Task<IActionResult> Summary()
        {
            var query = new SummaryQuery();

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
