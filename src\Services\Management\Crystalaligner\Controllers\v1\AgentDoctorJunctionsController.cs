﻿using Crystalaligner.Application.Commands.AgentDoctorJunctions;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1;
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class AgentDoctorJunctionsController : BaseController
{
    private readonly IMediator _mediatr;
    public AgentDoctorJunctionsController(IMediator mediatr) => _mediatr = mediatr;
        
    [HttpPost]
    public async Task<IActionResult> Post([FromBody] AgentDoctorJunctionCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }    

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<AgentDoctorJunction, bool>> predicate = s => s.Id == id;

        var query = new AgentDoctorJunctionDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

