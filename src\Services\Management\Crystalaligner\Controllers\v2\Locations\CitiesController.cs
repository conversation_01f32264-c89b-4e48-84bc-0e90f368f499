﻿using Crystalaligner.Application.Queries.MultipleQuery.Locations;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Domain.Entities.Locations;
using Crystalaligner.Management.Domain.Entities.Locations;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2.Locations
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    public class CitiesController : BaseController
    {
        private readonly IMediator _mediatr;
        public CitiesController(IMediator mediatr) => _mediatr = mediatr;

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = Array.Empty<string>();

            Expression<Func<City, bool>> predicate = s => s.Id > 0;

            var query = new CitiesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("GetPagedCountries")]
        public async Task<IActionResult> GetPagedCountries([FromQuery] PaginationFilterQuery filterQuery)
        {
            var request = new PagedFilterRequest<Country>(filterQuery);
            if (filterQuery.Filters != null)
            {
                request.Predicate = request.Predicate.Filter(request.FilterQuery);
                request.Predicate = request.Predicate.And(x => !x.IsDelete);
            }
            request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
            var query = new CountriesPagedQuery(request);
            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("GetCitiesByCountryId/{id}")]
        public async Task<IActionResult> GetCitiesByCountryId(int id)
        {
            var includes = Array.Empty<string>();

            Expression<Func<City, bool>> predicate = s => s.Id > 0 && s.CountryId == id;

            var query = new CitiesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("GetDistrictsByCityId/{cityId}")]
        public async Task<IActionResult> GetDistrictsByCityId(int cityId)
        {
            var includes = Array.Empty<string>();

            Expression<Func<District, bool>> predicate = s => s.Id > 0 && s.CityId == cityId;

            var query = new DistrictsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
