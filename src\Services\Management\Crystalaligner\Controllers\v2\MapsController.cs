﻿using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2;
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("2.0")]
[BasicAuthentication]
[ApiController]
public class MapsController : BaseController
{
    private readonly IMediator _mediatr;
    public MapsController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<Map, bool>> predicate = s => s.Id > 0 && s.IsDelete == false;

        var query = new MapsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Map>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new MapsPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
 
     
}

