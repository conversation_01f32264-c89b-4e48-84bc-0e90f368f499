﻿using Crystalaligner.Application.Commands.FirstCaseFeatures;
using Crystalaligner.Application.Commands.FirstCases;
using Crystalaligner.Application.Commands.WhoAreWeFeatures;
using Crystalaligner.Application.Queries.MultipleQuery.FirstCases;
using Crystalaligner.Application.Queries.SingleQuery.FirstCases;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Tools;
using DocumentFormat.OpenXml.Office2010.Excel;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class FirstCaseController : BaseController
    {
        private readonly IMediator _mediatr;
        private readonly IConfiguration _configuration;
        public FirstCaseController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<FirstCase, bool>> predicate = s => s.Id > 0;

            var query = new FirstCasesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<FirstCase, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new FirstCaseQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            Expression<Func<FirstCase, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new FirstCaseQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpPost("Post")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Post(IFormFile image, [FromForm] int LanguageId, [FromForm] string Content  )
        {
            FirstCaseCreate command = new FirstCaseCreate();
            command.LanguageId = LanguageId;
            command.Content = Content; 
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/FirstCase"))
                    client.CreateDirectory("Resources/FirstCase");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/FirstCase/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/FirstCase/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/FirstCase/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("Put")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image, [FromForm] int LanguageId, [FromForm] string Content, [FromForm] int Id )
        {
            FirstCaseUpdate command = new FirstCaseUpdate();
            command.LanguageId = LanguageId;
            command.Content = Content;
            command.Id = Id;
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/FirstCase"))
                    client.CreateDirectory("Resources/FirstCase");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/FirstCase/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/FirstCase/{newFileName}");
                    }

                    command.Image = "https://cdn.crystalaligner.com/" + $"Resources/FirstCase/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }



        [HttpPost("FirstCaseFeaturePost")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> FirstCaseFeaturePost(IFormFile image
            , [FromForm] int LangugageId
            , [FromForm] string Title
            , [FromForm] string Description)
        {
            FirstCaseFeatureCreate command = new FirstCaseFeatureCreate();
            command.LanguageId = LangugageId;
            command.Description = Description;
            command.Title = Title;

            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/Testimonial"))
                    client.CreateDirectory("Resources/Testimonial");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/Testimonial/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/Testimonial/{newFileName}");
                    }

                    command.Icon = "https://cdn.crystalaligner.com/" + $"Resources/Testimonial/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut("FirstCaseFeaturePut")]
        public async Task<IActionResult> FirstCaseFeaturePut([FromBody] FirstCaseFeatureUpdate command)
        {
            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpDelete("FirstCaseFeatureDelete/{id}")]
        public async Task<IActionResult> FirstCaseFeatureDelete(int id)
        {
            Expression<Func<FirstCaseFeature, bool>> predicate = s => s.Id == id;

            var query = new FirstCaseFeatureDelete(predicate);
            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
