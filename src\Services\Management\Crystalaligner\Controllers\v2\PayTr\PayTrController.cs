﻿using Crystalaligner.Application.Queries.SingleQuery.PayTr;
using Crystalaligner.Management.Tools;
using Crystalaligner.Model.Requests.PayTr;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Collections.Specialized;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace Crystalaligner.Controllers.v2.PayTr;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiController]
[ApiVersion("2.0")]
[BasicAuthentication]
public class PayTrController : ControllerBase
{
    private readonly IMediator _mediator;

    public PayTrController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet("{code}")]
    public async Task<IActionResult> Get(string code)
    {
        PayTrNotificationLogQuery query = new(code);

        var result = await _mediator.Send(query);

        if (result == null) return NotFound(result);

        return result.Success ? Ok(result) : Accepted(result);
    }

    [HttpGet("GetInstallmentRatio")]
    public async Task<IActionResult> GetInstallmentRatio()
    {
        var config = new ConfigurationBuilder()
        .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
        .Build();

        string merchant_id = config.GetSection("PayTR")["MagazaNo"];
        string merchant_key = config.GetSection("PayTR")["Key"];
        string merchant_salt = config.GetSection("PayTR")["Anahtar"];

        // İstek ID: İstekler için belirlediğiniz benzersiz numara
        string request_id = Guid.NewGuid().ToString().Replace("-", "");

        // Gönderilecek veriler oluşturuluyor
        NameValueCollection data = new NameValueCollection();
        data["merchant_id"] = merchant_id;
        data["request_id"] = request_id;

        // Token oluşturma fonksiyonu, değiştirilmeden kullanılmalıdır.
        string Birlestir = string.Concat(merchant_id, request_id, merchant_salt);
        HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(merchant_key));
        byte[] b = hmac.ComputeHash(Encoding.UTF8.GetBytes(Birlestir));
        data["paytr_token"] = Convert.ToBase64String(b);

        using WebClient client = new WebClient();
        client.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        byte[] result = client.UploadValues("https://www.paytr.com/odeme/taksit-oranlari", "POST", data);
        string ResultAuthTicket = Encoding.UTF8.GetString(result);
        dynamic json = JValue.Parse(ResultAuthTicket);

        if (json.status == "success")
        {
            //VT işlemleri vs.
            //Response.Write(json);
            return Ok(json);
        }
        else
        {
            //Örn. $result -> array('status'=>'error', "err_msg" => "Zorunlu alan degeri gecersiz veya gonderilmedi: ")
            //Response.Write(json.err_msg);
            return Accepted(json);
        }
    }

    [HttpGet("GetBinDetail")]
    public IActionResult GetBinDetail(string cardNumber)
    {
        var config = new ConfigurationBuilder()
        .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
        .Build();

        string merchant_id = config.GetSection("PayTR")["MagazaNo"];
        string merchant_key = config.GetSection("PayTR")["Key"];
        string merchant_salt = config.GetSection("PayTR")["Anahtar"];

        //
        // Sorgulama yapılmak istenen karta ait kart numarasının ilk 6 veya 8 hanesi. Maksimum doğrulama için 8 hane kullanın.
        string bin_number = cardNumber;
        //
        // ###########################################################################
        // Token oluşturma fonksiyonu, değiştirilmeden kullanılmalıdır.
        string Birlestir = string.Concat(bin_number, merchant_id, merchant_salt);
        HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(merchant_key));
        byte[] b = hmac.ComputeHash(Encoding.UTF8.GetBytes(Birlestir));
        string paytr_token = Convert.ToBase64String(b);

        // Gönderilecek veriler oluşturuluyor
        NameValueCollection data = new NameValueCollection();
        data["merchant_id"] = merchant_id;
        data["bin_number"] = bin_number;
        data["paytr_token"] = paytr_token;
        //
        using (WebClient client = new WebClient())
        {
            client.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
            byte[] result = client.UploadValues("https://www.paytr.com/odeme/api/bin-detail", "POST", data);
            string ResultAuthTicket = Encoding.UTF8.GetString(result);
            dynamic json = JValue.Parse(ResultAuthTicket);
            if (json.status == "success")
            {
                return Ok(json);
                //Response.Write(json);
            }
            else if (json.status == "failed")
            {
                return Ok("{\r\n  \"status\": \"error\",\r\n  \"brand\": \"\",\r\n  \"cardType\": \"\",\r\n  \"businessCard\": \"\",\r\n  \"bank\": \"\",\r\n  \"schema\": \"\",\r\n  \"bankCode\": 0,\r\n  \"allow_non3d\": \"\"\r\n}");
                //Response.Write("BIN tanımlı değil. (Örneğin bir yurtdışı kartı)");
            }
            else if (json.status == "error")
            {
                return BadRequest(json);
                //Response.Write("PAYTR BIN detail request error. Error:" + json.err_msg + "");
            }
        }

        return BadRequest();
    }

    /// <summary>
    /// Ödeme işlemi sonrası PayTR nin bize dönüş yapacağı URL dir
    /// </summary>
    /// <returns></returns>
    [HttpPost("Success")]
    [AllowAnonymous]
    public async Task Success([FromForm] PaymentReturnRequest request)
    {
        var result = await _mediator.Send(request);

        if (result.Success)
        {
            await Response.WriteAsync("OK");
        }
        else
        {
            await Response.WriteAsync("PAYTR notification failed: bad hash");
        }
    }
}
