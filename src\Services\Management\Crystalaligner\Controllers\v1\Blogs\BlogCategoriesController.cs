﻿using Crystalaligner.Application.Commands.Blogs;
using Crystalaligner.Application.Queries.MultipleQuery.Blogs;
using Crystalaligner.Application.Queries.SingleQuery.Blogs;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Blogs;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Blogs;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class BlogCategoriesController : BaseController
{
    private readonly IMediator _mediatr;
    public BlogCategoriesController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<BlogCategory>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new BlogCategoriesPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Blog_Kategori_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };
        Expression<Func<BlogCategory, bool>> predicate = s => s.Id > 0;
        var query = new BlogCategoriesQuery(predicate, includes);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound(result);
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<BlogCategory, bool>> predicate = s => s.Id == id;
        var includes = new string[] { };
        var query = new BlogCategoryQuery(predicate, includes);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<BlogCategory>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new BlogCategoriesPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] BlogCategoryCreate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound(result);
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] BlogCategoryUpdate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound(result);
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<BlogCategory, bool>> predicate = s => s.Id == id;
        var query = new BlogCategoryDelete(predicate);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }
}
