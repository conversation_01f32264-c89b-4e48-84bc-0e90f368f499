﻿using Crystalaligner.Application.Commands.Galleries;
using Crystalaligner.Application.Queries.MultipleQuery.Galleries;
using Crystalaligner.Application.Queries.SingleQuery.Galleries;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class GalleryController : BaseController
    {
        private readonly IMediator _mediatr;
        public GalleryController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Gallery, bool>> predicate = s => s.Id > 0;

            var query = new GalleriesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        
    }
}
