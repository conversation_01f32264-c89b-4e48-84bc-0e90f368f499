﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities.Orders;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class OrderUserPackageJunctionsController : BaseController
{
    private readonly IMediator _mediatr;
    public OrderUserPackageJunctionsController(IMediator mediatr) => _mediatr = mediatr;


    [HttpPost]
    public async Task<IActionResult> Post([FromBody] OrderUserPackageJunctionCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<OrderUserPackageJunction, bool>> predicate = s => s.Id == id;

        var query = new OrderUserPackageJunctionDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}

