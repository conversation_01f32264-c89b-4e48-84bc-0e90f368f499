using AutoMapper;
using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Helpers;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Management.Domain.Entities.Order.Orders;
using Crystalaligner.Management.Domain.Repositories.Base;
using Crystalaligner.Management.Model.Responses.Orders;
using MediatR;

namespace Crystalaligner.Application.Handlers.TreatmentPlans;

public class TreatmentPlanUpdateHandler : IRequestHandler<TreatmentPlanUpdate, ApiResponse<TreatmentPlanResponse>>
{
    private readonly IUnitOfWork _repo;
    private readonly IMapper _mapper;

    public TreatmentPlanUpdateHandler(IUnitOfWork repo, IMapper mapper)
    {
        _repo = repo;
        _mapper = mapper;
    }
    public async Task<ApiResponse<TreatmentPlanResponse>> Handle(TreatmentPlanUpdate request, CancellationToken cancellationToken)
    {
        var org = await _repo.TreatmentPlans.GetAsync(p => p.Id == request.Id);
        if (org == null)
            return new ErrorApiResponse<TreatmentPlanResponse>(ResultMessage.NotFound);

        // Reason değişikliğini kontrol et (güncelleme öncesi)
        var reasonChanged = org.Reason != request.Reason;

        // Mevcut entity'yi güncelle, yeni instance oluşturma
        org.OrderId = request.OrderId;
        org.IsApproved = request.IsApproved;
        org.Reason = request.Reason;
        org.VideoUrl = request.VideoUrl;
        org.Comment = request.Comment; // Bu satır artık çalışacak
        org.AnimationUrl = request.AnimationUrl;

        // PDF URL'leri sadece null değilse güncelle
        if (request.PdfUrl != null)
            org.PdfUrl = request.PdfUrl;

        if (request.PdfUrlSecond != null)
            org.PdfUrlSecond = request.PdfUrlSecond;

        if (request.PdfUrlThird != null)
            org.PdfUrlThird = request.PdfUrlThird;

        var model = await _repo.TreatmentPlans.UpdateAsync(org);
        if (model.Success)
        {
            if (reasonChanged)
            {
                var order = await _repo.Orders.GetAsync(a => a.Id == org.OrderId);
                order.Status = Helper.Enumerations.OrderStatus.DemandRearrange;
                await _repo.Orders.UpdateAsync(order);
            }

            var response = _mapper.Map<TreatmentPlanResponse>(org);
            return new SuccessApiResponse<TreatmentPlanResponse>(response);
        }
        return new ErrorApiResponse<TreatmentPlanResponse>(ResultMessage.NotUpdated);
    }
}

