﻿using Crystalaligner.Application.Commands.Products;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities.Products;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Chats;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class ProductPriceHistoriesController : BaseController
{
    private readonly IMediator _mediatr;
    public ProductPriceHistoriesController(IMediator mediatr) => _mediatr = mediatr;
    
    [HttpPost]
    public async Task<IActionResult> Post([FromBody] ProductPriceHistoryCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }


    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<ProductPriceHistory, bool>> predicate = s => s.Id == id;

        var query = new ProductPriceHistoryDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
