﻿using Crystalaligner.Application.Commands.Testimonials;
using Crystalaligner.Application.Queries.MultipleQuery.Testimonials;
using Crystalaligner.Application.Queries.SingleQuery.Testimonials;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [ApiController, Authorize]
    public class TestimonialController : BaseController
    {
        private readonly IConfiguration _configuration;
        private readonly IMediator _mediatr;
        public TestimonialController(IMediator mediatr, IConfiguration configuration)
        {
            _mediatr = mediatr;
            _configuration = configuration;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var includes = new string[] { };

            Expression<Func<Testimonial, bool>> predicate = s => s.Id > 0;

            var query = new TestimonialsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            Expression<Func<Testimonial, bool>> predicate = s => s.Id == id;

            var includes = new string[] { };

            var query = new TestimonialQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpGet("GetByLangugageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            var includes = new string[] { };

            Expression<Func<Testimonial, bool>> predicate = s => s.LangugageId == LanguageId;

            var query = new TestimonialsQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            //ApiResponse<TestimonialResponse> data = new ApiResponse<TestimonialResponse>();
            //data.Success = true;
            //data.Data = result.Data.FirstOrDefault();
            return result.Success ? Success(result) : BadRequest(result);
        }
        [HttpPost]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Post(IFormFile image
            , [FromForm] int LangugageId
            , [FromForm] string Title
            , [FromForm] string Body
            , [FromForm] string Doctor
            , [FromForm] string DoctorTitle
            , [FromForm] decimal Star
            )
        {
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            TestimonialCreate command = new TestimonialCreate();
            command.LangugageId = LangugageId;
            command.Title = Title;
            command.Body = Body;
            command.Doctor = Doctor;
            command.DoctorTitle = DoctorTitle;
            command.Star = Star;
            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/Testimonial"))
                    client.CreateDirectory("Resources/Testimonial");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/Testimonial/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/Testimonial/{newFileName}");
                    }

                    command.DoctorImage = "https://cdn.crystalaligner.com/" + $"Resources/Testimonial/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpPut]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Put(IFormFile image
            , [FromForm] int Id
            , [FromForm] int LangugageId
            , [FromForm] string Title
            , [FromForm] string Body
            , [FromForm] string Doctor
            , [FromForm] string DoctorTitle
            , [FromForm] decimal Star
            )
        {
            var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
            string ftpServer = cdnConfig.FtpServer;
            string username = cdnConfig.Username;
            string password = cdnConfig.Password;

            TestimonialUpdate command = new TestimonialUpdate();
            command.Id = Id;
            command.LangugageId = LangugageId;
            command.Title = Title;
            command.Body = Body;
            command.Doctor = Doctor;
            command.DoctorTitle = DoctorTitle;
            command.Star = Star;

            try
            {
                var client = new FtpClient(ftpServer, username, password, 21);
                client.Connect();

                if (!client.DirectoryExists("Resources/Testimonial"))
                    client.CreateDirectory("Resources/Testimonial");

                string imageName = Guid.NewGuid().ToString();

                if (image is not null && image.Length > 0)
                {
                    var newName = image.Name.Replace("file", "");
                    string newFileName = imageName + "-" + newName + Path.GetExtension(image.FileName);

                    string tempFilePath = Path.GetTempFileName();

                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        image.CopyTo(stream);
                    }


                    if (!client.FileExists($"Resources/Testimonial/{image.FileName}"))
                    {
                        using var fileStream = System.IO.File.OpenRead(tempFilePath);
                        client.UploadFile(tempFilePath, $"Resources/Testimonial/{newFileName}");
                    }

                    command.DoctorImage = "https://cdn.crystalaligner.com/" + $"Resources/Testimonial/{newFileName}";
                }


                client.Disconnect();
            }
            catch (Exception)
            {

            }

            var result = await _mediatr.Send(command);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            Expression<Func<Testimonial, bool>> predicate = s => s.Id == id;

            var query = new TestimonialDelete(predicate);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
