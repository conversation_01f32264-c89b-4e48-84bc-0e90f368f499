﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Text;

namespace Crystalaligner.Management.Tools
{
    public class BasicAuthenticationAttribute : ActionFilterAttribute
    {
        //jwt authentication kullandığımız için burayı auth olarak kullanınca çakışıyor o yüzden action filter olarak eklendi
        public override void OnActionExecuting(ActionExecutingContext actionContext)
        {

            if (actionContext.HttpContext.Request.Headers.Authorization.Count == 0)
            {
                actionContext.Result = new UnauthorizedObjectResult(HttpStatusCode.Unauthorized);
                return;
            }


            string token = actionContext.HttpContext.Request.Headers.Authorization[0].Replace("Basic ", "");
            if (!Auth.ValidateCurrentToken(token))
            {
                actionContext.Result = new UnauthorizedObjectResult(HttpStatusCode.Unauthorized);
                return;
            }
        }
    }

    public class Auth
    {
        public static bool ValidateCurrentToken(string token)
        {
            string crendentials = Encoding.GetEncoding("UTF-8").GetString(Convert.FromBase64String(token));
            var splitCredentials = crendentials.Split(':');
            string userName = splitCredentials[0];
            string password = splitCredentials[1];

            return CheckUser(userName, password);
        }
        static bool CheckUser(string userName, string password)
        {
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();

            string appUserName = config.GetSection("BasicAuth")["UserName"];
            string appPassword = config.GetSection("BasicAuth")["Password"];

            return userName == appUserName && password == appPassword;
        }
    }
}
