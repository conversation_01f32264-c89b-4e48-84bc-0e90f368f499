﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Commands.Users;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Users;
using Crystalaligner.Application.Queries.SingleQuery.PayTr;
using Crystalaligner.Application.Queries.SingleQuery.Users;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.EmailHelper;
using Crystalaligner.Core.Base.Helpers.GenericExpressions;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Domain.Entities.PayTr;
using Crystalaligner.Management.Domain.Entities;
using Crystalaligner.Management.Domain.Entities.Orders;
using Crystalaligner.Management.Domain.Entities.Users;
using Crystalaligner.Model.Requests.PayTr;
using Crystalaligner.Model.Responses.Users;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using static Crystalaligner.Helper.Enumerations;

namespace Crystalaligner.Management.Controllers.v1.Users;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class UsersController : BaseController
{
    private readonly IMediator _mediatr;
    public static IConfiguration _configuration { get; set; }
    public UsersController(IConfiguration configuration, IMediator mediatr)
    {
        _configuration = configuration;
        _mediatr = mediatr;
    }

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<User>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            if (request.Predicate == null)
                request.Predicate = x => true;
            else
                request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new UsersPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();

        var queryOrder = new OrdersQuery();
        var resultOrder = await _mediatr.Send(queryOrder);

        var queryAgentv = new UsersQuery(a => a.UserType == UserType.Agent);
        var resultAgent = await _mediatr.Send(queryAgentv);

        Expression<Func<UserAddress, bool>> predicate = s => s.UserId > 0;
        var includes = new string[] { "City", "District" };
        var userAdressQuery = new UserAddressesQuery(predicate, includes);
        var resultuserAdress = await _mediatr.Send(userAdressQuery);

        List<DoctorExcelResponse> responses = new List<DoctorExcelResponse>();
        foreach (var item in result.Data)
        {

            var adres = resultuserAdress.Data.Where(a => a.UserId == item.Id).FirstOrDefault();
            var agent = resultAgent.Data.Where(s => s.Id == item.AgentId).FirstOrDefault();
            DoctorExcelResponse doctor = new DoctorExcelResponse();
            doctor.Doktor = item.Name;
            doctor.Telefon = item.Phone;
            doctor.KayitTarihi = item.CreatedDate?.ToString("dd.MM.yyyy");
            doctor.Adres = adres?.Address + " " + adres?.District.Name + " / " + adres?.City.Name;
            doctor.VakaSayisi = resultOrder.Data.Where(a => a.CreatedId == item.Id).Count().ToString();
            doctor.TemsilciAdi = agent?.Name;
            doctor.TemsilciId = agent == null ? 0 : agent.Id;
            doctor.Mail = item.Email;
            responses.Add(doctor);
        }

        var excelFile = CreateExcelFile(responses);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Kullanici_Listesi.xlsx");
    }

    [HttpGet("GetPartialPayments")]
    public async Task<IActionResult> GetPartialPayments()
    {
        var includes = new string[] { "User" };

        Expression<Func<PayTrNotificationLog, bool>> predicate = s => s.PaymentAmount > 0
        && s.UserId > 0
        && s.ProductId == 33
        && s.User.IsActive == true
        && !s.User.IsDelete;

        var query = new PayTrNotificationLogPartialPaymentQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<User, bool>> predicate = s => s.Id > 0;

        var query = new UsersQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    //    [HttpGet("SendActivationMailAgain")]
    //    public async Task<IActionResult> SendActivationMailAgain([FromQuery] int id)
    //    {
    //        Expression<Func<User, bool>> predicate = s => s.Id == id;
    //        var includes = Array.Empty<string>();
    //        var query = new UserQuery(predicate, includes);
    //        var result = await _mediatr.Send(query);
    //        if (result == null) return NotFound();

    //        if (result.Success)
    //        {
    //            var mailSettings = _configuration
    //                        .GetSection("SmtpHelper")
    //                        .Get<EmailSenderModel>();
    //            string mail = mailSettings.Mail;
    //            string password = mailSettings.Password;
    //            string host = mailSettings.Host;
    //            int port = mailSettings.Port;
    //            string url = $"'https://api.crystalaligner.com/api/v1/Users/<USER>/{result.Data.Id}/{result.Data.Code}'";
    //#if DEBUG
    //            url = $"'http://testapi.crystalaligner.com/api/v1/Users/<USER>/{result.Data.Id}/{result.Data.Code}'";
    //#endif

    //            string html = GenerateMailBody(url);
    //            EmailSenderModel sender = new()
    //            {
    //                Mail = mail,
    //                Password = password,
    //                Host = host,
    //                Port = port,
    //                Body = html,
    //                Subject = "Please verify your email account",
    //                To = result.Data.Email
    //            };

    //            EmailSender send = new EmailSender();
    //            await send.SendEmailAsync(sender);
    //            return Success(result);
    //        }
    //        return BadRequest(result);
    //    }

    [HttpGet("GetByUserTypeId")]
    public async Task<IActionResult> GetByUserTypeId([FromQuery] PaginationFilterQueryV1 filterQuery)
    {
        if (filterQuery.PageSize == 1000)
        {
            filterQuery.PageSize = 100000;
        }
        var request = new PagedFilterRequestV1<User>(filterQuery);

        if (filterQuery.Field.IndexOf(',') > -1)
        {
            var selectedUserTypes = filterQuery.Field.Split(',').Select(int.Parse).ToArray();
            Expression<Func<User, bool>> predicate = s => selectedUserTypes.Contains((int)s.UserType);
            request.Predicate = predicate;
        }
        else
        {
            Expression<Func<User, bool>> predicate = s => (int)s.UserType == Convert.ToInt32(filterQuery.Field);
            request.Predicate = predicate;
        }
        var query = new UsersPagedByFilterQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetDoctorsByAgentId")]
    public async Task<IActionResult> GetDoctorsByAgentId([FromQuery] PaginationFilterQueryV1 filterQuery)
    {
        var request = new PagedFilterRequestV1<User>(filterQuery);

        if (filterQuery.Field.IndexOf(',') > -1)
        {
            var selectedUserTypes = filterQuery.Field.Split(',').Select(int.Parse).ToArray();
            Expression<Func<User, bool>> predicate = s => selectedUserTypes.Contains((int)s.AgentId);
            request.Predicate = predicate;
        }
        else
        {
            Expression<Func<User, bool>> predicate = s => (int)s.AgentId == Convert.ToInt32(filterQuery.Field);
            request.Predicate = predicate;
        }
        var query = new UsersPagedByFilterQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByFilter")]
    public async Task<IActionResult> GetByFilter([FromQuery] PaginationFilterQueryV1 filterQuery)
    {
        // get prop. name from request
        var propertyName = filterQuery.Sort;
        if (string.IsNullOrEmpty(filterQuery.Sort))
            propertyName = "Id";
        var property = typeof(User).GetProperty(propertyName);

        var request = new PagedFilterRequestV1<User>(
        filterQuery: new PaginationFilterQueryV1(),
        predicate: u => u.UserType == UserType.Doctor
                    &&
                    string.IsNullOrEmpty(filterQuery.Field) ? true :
                    (u.Name.Contains(filterQuery.Field) || u.Orders.Any(o => o.Patient.Name.Contains(filterQuery.Field))),
        includePaths: query => query.Include(entity => entity.Orders).ThenInclude(entity => entity.Patient),
        orderBy: OrderExpression.CreateOrderByExpression<User>(property)

        );
        if (filterQuery.PageSize != null)
            request.FilterQuery.PageSize = filterQuery.PageSize;

        if (filterQuery.PageNumber != null)
            request.FilterQuery.PageNumber = filterQuery.PageNumber;


        var query = new UsersPagedByFilterQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<User>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            if (request.Predicate == null)
                request.Predicate = x => true;
            else
                request.Predicate = request.Predicate.And(x => !x.IsDelete);

        }
        // Include kullanımı
        //request.IncludePaths = p => p.Include(x => x.Company);
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new UsersPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("ChangePassword")]
    public async Task<IActionResult> ChangePassword([FromBody] UserPasswordChange command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("ChangeDentistName")]
    public async Task<IActionResult> ChangeDentistName([FromBody] UserNameChange command)
    {
        if (command.Id > 0)
        {
            var result = await _mediatr.Send(command);
            if (result == null) return NotFound();
            return result.Success ? Success(result) : BadRequest(result);
        }
        else
        {
            return BadRequest();
        }
    }

    [HttpPost("DoctorApproved")]
    public async Task<IActionResult> DoctorApproved([FromBody] DoctorApproved command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    /// <summary>
    /// Admin = 1 olacak şekilde başlıyor
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetUserTypes")]
    public IActionResult GetUserTypes()
    {
        var lst = Enum.GetValues(typeof(Crystalaligner.Helper.Enumerations.UserType)).Cast<UserType>().Select(x => x.ToString()).ToList();
        return Ok(lst);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<User, bool>> predicate = s => s.Id == id;
        var includes = Array.Empty<string>();
        var query = new UserQuery(predicate, includes);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("ResetPassword")]
    public async Task<IActionResult> ResetPassword([FromBody] UserPasswordReset command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }



    [HttpPost("ForgotPassword/{email}"), AllowAnonymous]
    public async Task<IActionResult> ForgotPassword(string email)
    {
        Expression<Func<User, bool>> predicate = s => s.Email == email;

        var includes = Array.Empty<string>();

        var query = new ForgotPasswordQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("CheckPasswordGuid/{guid}"), AllowAnonymous]
    public async Task<IActionResult> CheckPasswordGuid(string guid)
    {
        Expression<Func<User, bool>> predicate = s => guid != Guid.Empty.ToString() && s.Guid.ToString() == guid && s.GuidExpirationDate > DateTime.Now;

        var includes = Array.Empty<string>();

        var query = new CheckPasswordGuidQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpPost("Activation/{guid}"), AllowAnonymous]
    public async Task<IActionResult> Activation(string guid)
    {
        Expression<Func<User, bool>> predicate = s => guid != Guid.Empty.ToString() && s.Guid.ToString() == guid && s.IsActive == false;

        var includes = Array.Empty<string>();

        var query = new ActivationQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("Login"), AllowAnonymous]
    public async Task<IActionResult> Login([FromBody] LoginQuery command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound(result);

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] UserCreate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();

        if (result.Success)
        {
            //            var mailSettings = _configuration
            //                        .GetSection("MailSettings")
            //                        .Get<EmailSenderModel>();
            //            string mail = mailSettings.Mail;
            //            string password = mailSettings.Password;
            //            string host = mailSettings.Host;
            //            int port = mailSettings.Port;
            //            string url = $"'https://api.crystalaligner.com/api/v1/Users/<USER>/{result.Data.Id}/{result.Data.Code}'";
            //#if DEBUG
            //            url = $"'http://testapi.crystalaligner.com/api/v1/Users/<USER>/{result.Data.Id}/{result.Data.Code}'";
            //#endif

            //            string html = GenerateMailBody(url);
            //            html += "<p><a href= " + url + "  class='btn btn-primary'>Verify</a></p>";
            //            html += @"</td>
            //          		</tr>
            //          	</table>
            //          </td>
            //	      </tr><!-- end tr -->
            //	      <tr>
            //          <td valign=""middle"" class=""hero bg_white"" style=""padding: 3em 0 2em 0;"">
            //            <img src=""http://testcdn.crystalaligner.com/Resources/images/email.png"" alt="""" style=""width: 300px; max-width: 600px; height: auto; margin: auto; display: block;"">
            //          </td>
            //	      </tr><!-- end tr -->
            //				<tr>
            //          <td valign=""middle"" class=""hero bg_white"" style=""padding: 2em 0 4em 0;"">
            //            <table>
            //            	<tr>
            //            		<td>
            //            			<div class=""text"" style=""padding: 0 2.5em; text-align: center;"">
            //            				<h2>Please verify your email</h2>
            //            				<!-- <h3>Amazing deals, updates, interesting news right in your inbox</h3> -->
            //            				<p><a href=""#"" class=""btn btn-primary"">Verify</a></p>
            //            			</div>
            //            		</td>
            //            	</tr>
            //            </table>
            //          </td>
            //	      </tr>
            //    </div>
            //  </center>
            //</body>
            //</html>";
            //            EmailSenderModel sender = new()
            //            {
            //                Mail = mail,
            //                Password = password,
            //                Host = host,
            //                Port = port,
            //                Body = html,
            //                Subject = "Please verify your email account",
            //                To = command.Email
            //            };

            //            EmailSender send = new EmailSender();
            //            await send.SendEmailAsync(sender);
            return Success(result);
        }
        return BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] UserUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<User, bool>> predicate = s => s.Id == id;

        var query = new UserDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("SendPartialPayment")]
    public async Task<string> SendPartialPayment(CreatePayTRPartialPayment request)
    {
        var result = await _mediatr.Send(request);

        return result.Data;
    }

    [HttpPost("SendPayment")]
    public async Task<string> SendPayment(Crystalaligner.Model.Requests.PayTr.CreatePayTRPayment request)
    {
        var result = await _mediatr.Send(request);

        return result.Data;
    }

    [HttpPost("SendPaymentWithBasket")]
    public async Task<string> SendPaymentWithBasket(CreatePayTRPaymentWithBasket request)
    {
        var result = await _mediatr.Send(request);

        return result.Data;
    }

    private static string GenerateMailBody(string url)
    {
        string html = @"<!DOCTYPE html>
<html lang=""en"" xmlns=""http://www.w3.org/1999/xhtml"" xmlns:v=""urn:schemas-microsoft-com:vml"" xmlns:o=""urn:schemas-microsoft-com:office:office"">
<head>
    <meta charset=""utf-8""> <!-- utf-8 works for most cases -->
    <meta name=""viewport"" content=""width=device-width""> <!-- Forcing initial-scale shouldn't be necessary -->
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge""> <!-- Use the latest (edge) version of IE rendering engine -->
    <meta name=""x-apple-disable-message-reformatting"">  <!-- Disable auto-scale in iOS 10 Mail entirely -->
    <title></title> <!-- The title tag shows in email notifications, like Android 4.4. -->
    <link rel=""stylesheet"" href=""/css/emailCheck.css"" />
    <link href=""https://fonts.googleapis.com/css?family=Lato:300,400,700"" rel=""stylesheet"">
    <!-- CSS Reset : BEGIN -->
    <style>

        /* What it does: Remove spaces around the email design added by some email clients. */
        /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
        html,
body {
    margin: 0 auto !important;
    padding: 0 !important;
    height: 100% !important;
    width: 100% !important;
    background: #f1f1f1;
}

/* What it does: Stops email clients resizing small text. */
* {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

/* What it does: Centers email on Android 4.4 */
div[style*=""margin: 16px 0""] {
    margin: 0 !important;
}

/* What it does: Stops Outlook from adding extra spacing to tables. */
table,
td {
    mso-table-lspace: 0pt !important;
    mso-table-rspace: 0pt !important;
}

/* What it does: Fixes webkit padding issue. */
table {
    border-spacing: 0 !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    margin: 0 auto !important;
}

/* What it does: Uses a better rendering method when resizing images in IE. */
img {
    -ms-interpolation-mode:bicubic;
}

/* What it does: Prevents Windows 10 Mail from underlining links despite inline CSS. Styles for underlined links should be inline. */
a {
    text-decoration: none;
}

/* What it does: A work-around for email clients meddling in triggered links. */
*[x-apple-data-detectors],  /* iOS */
.unstyle-auto-detected-links *,
.aBn {
    border-bottom: 0 !important;
    cursor: default !important;
    color: inherit !important;
    text-decoration: none !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
}

/* What it does: Prevents Gmail from displaying a download button on large, non-linked images. */
.a6S {
    display: none !important;
    opacity: 0.01 !important;
}

/* What it does: Prevents Gmail from changing the text color in conversation threads. */
.im {
    color: inherit !important;
}

/* If the above doesn't work, add a .g-img class to any image in question. */
img.g-img + div {
    display: none !important;
}

/* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
/* Create one of these media queries for each additional viewport size you'd like to fix */

/* iPhone 4, 4S, 5, 5S, 5C, and 5SE */
@media only screen and (min-device-width: 320px) and (max-device-width: 374px) {
    u ~ div .email-container {
        min-width: 320px !important;
    }
}
/* iPhone 6, 6S, 7, 8, and X */
@media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
    u ~ div .email-container {
        min-width: 375px !important;
    }
}
/* iPhone 6+, 7+, and 8+ */
@media only screen and (min-device-width: 414px) {
    u ~ div .email-container {
        min-width: 414px !important;
    }
}

    </style>

    <!-- CSS Reset : END -->

    <!-- Progressive Enhancements : BEGIN -->
    <style>

	    .primary{
	background: #30e3ca;
}
.bg_white{
	background: #ffffff;
}
.bg_light{
	background: #fafafa;
}
.bg_black{
	background: #000000;
}
.bg_dark{
	background: rgba(0,0,0,.8);
}
.email-section{
	padding:2.5em;
}

/*BUTTON*/
.btn{
	padding: 10px 15px;
	display: inline-block;
}
.btn.btn-primary{
	border-radius: 5px;
	background: #30e3ca;
	color: #ffffff;
}
.btn.btn-white{
	border-radius: 5px;
	background: #ffffff;
	color: #000000;
}
.btn.btn-white-outline{
	border-radius: 5px;
	background: transparent;
	border: 1px solid #fff;
	color: #fff;
}
.btn.btn-black-outline{
	border-radius: 0px;
	background: transparent;
	border: 2px solid #000;
	color: #000;
	font-weight: 700;
}

h1,h2,h3,h4,h5,h6{
	font-family: 'Lato', sans-serif;
	color: #000000;
	margin-top: 0;
	font-weight: 400;
}

body{
	font-family: 'Lato', sans-serif;
	font-weight: 400;
	font-size: 15px;
	line-height: 1.8;
	color: rgba(0,0,0,.4);
}

a{
	color: #30e3ca;
}

table{
}
/*LOGO*/

.logo h1{
	margin: 0;
}
.logo h1 a{
	color: #30e3ca;
	font-size: 24px;
	font-weight: 700;
	font-family: 'Lato', sans-serif;
}

/*HERO*/
.hero{
	position: relative;
	z-index: 0;
}

.hero .text{
	color: rgba(0,0,0,.3);
}
.hero .text h2{
	color: #000;
	font-size: 40px;
	margin-bottom: 0;
	font-weight: 400;
	line-height: 1.4;
}
.hero .text h3{
	font-size: 24px;
	font-weight: 300;
}
.hero .text h2 span{
	font-weight: 600;
	color: #30e3ca;
}


/*HEADING SECTION*/
.heading-section{
}
.heading-section h2{
	color: #000000;
	font-size: 28px;
	margin-top: 0;
	line-height: 1.4;
	font-weight: 400;
}
.heading-section .subheading{
	margin-bottom: 20px !important;
	display: inline-block;
	font-size: 13px;
	text-transform: uppercase;
	letter-spacing: 2px;
	color: rgba(0,0,0,.4);
	position: relative;
}
.heading-section .subheading::after{
	position: absolute;
	left: 0;
	right: 0;
	bottom: -10px;
	content: '';
	width: 100%;
	height: 2px;
	background: #30e3ca;
	margin: 0 auto;
}

.heading-section-white{
	color: rgba(255,255,255,.8);
}
.heading-section-white h2{
	font-family: 
	line-height: 1;
	padding-bottom: 0;
}
.heading-section-white h2{
	color: #ffffff;
}
.heading-section-white .subheading{
	margin-bottom: 0;
	display: inline-block;
	font-size: 13px;
	text-transform: uppercase;
	letter-spacing: 2px;
	color: rgba(255,255,255,.4);
}


ul.social{
	padding: 0;
}
ul.social li{
	display: inline-block;
	margin-right: 10px;
}

/*FOOTER*/

.footer{
	border-top: 1px solid rgba(0,0,0,.05);
	color: rgba(0,0,0,.5);
}
.footer .heading{
	color: #000;
	font-size: 20px;
}
.footer ul{
	margin: 0;
	padding: 0;
}
.footer ul li{
	list-style: none;
	margin-bottom: 10px;
}
.footer ul li a{
	color: rgba(0,0,0,1);
}


@media screen and (max-width: 500px) {


}


    </style>


</head>

<body width=""100%"" style=""margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f1f1f1;"">
	<center style=""width: 100%; background-color: #f1f1f1;"">
    <div style=""display: none; font-size: 1px;max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden; mso-hide: all; font-family: sans-serif;"">
      &zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;
    </div>
    <div style=""max-width: 600px; margin: 0 auto;"" class=""email-container"">
    	<!-- BEGIN BODY -->
      <table align=""center"" role=""presentation"" cellspacing=""0"" cellpadding=""0"" border=""0"" width=""100%"" style=""margin: auto;"">
      	<tr>
          <td valign=""top"" class=""bg_white"" style=""padding: 1em 2.5em 0 2.5em;"">
          	<table role=""presentation"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
          		<tr>
          			<td class=""logo"" style=""text-align: center;"">";
        html += "<p><a href= " + url + "  class='btn btn-primary'>Verify</a></p>";
        html += @"</td>
          		</tr>
          	</table>
          </td>
	      </tr><!-- end tr -->
	      <tr>
          <td valign=""middle"" class=""hero bg_white"" style=""padding: 3em 0 2em 0;"">
            <img src=""http://testcdn.crystalaligner.com/Resources/images/email.png"" alt="""" style=""width: 300px; max-width: 600px; height: auto; margin: auto; display: block;"">
          </td>
	      </tr><!-- end tr -->
				<tr>
          <td valign=""middle"" class=""hero bg_white"" style=""padding: 2em 0 4em 0;"">
            <table>
            	<tr>
            		<td>
            			<div class=""text"" style=""padding: 0 2.5em; text-align: center;"">
            				<h2>Please verify your email</h2>
            				<!-- <h3>Amazing deals, updates, interesting news right in your inbox</h3> -->
            				<p><a href=""#"" class=""btn btn-primary"">Verify</a></p>
            			</div>
            		</td>
            	</tr>
            </table>
          </td>
	      </tr>
    </div>
  </center>
</body>
</html>";
        return html;
    }

    //[AllowAnonymous]
    //[HttpGet("activate/{id}/{code}")]
    //public async Task<IActionResult> Activate(int id, Guid code)
    //{
    //    var query = new UserActivatingQuery(null, null);
    //    query.Id = id;
    //    query.Code = code;
    //    var result = await _mediatr.Send(query);
    //    if (result == null) return NotFound();
    //    return result.Success ? Ok() : BadRequest();
    //}

    [HttpPost("SendEmail")]
    public async Task<IActionResult> SendEmail([FromBody] EmailModel emailModel)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var mailInfo = _configuration.GetSection("SmtpHelper").Get<EmailSenderModel>();

        if (mailInfo is not null)
        {
            var smtpClientHelper = new SmtpClientHelper(mailInfo.Host, mailInfo.Port, mailInfo.Mail, mailInfo.Password, false);
            try
            {
                await smtpClientHelper.SendEmailAsync(
                    mailInfo.Mail,
                    emailModel.ToEmail,
                    emailModel.Subject,
                    emailModel.Body
                );

                return Ok("E-posta başarıyla gönderildi.");
            }
            catch (Exception ex)
            {
                return BadRequest("E-posta gönderimi sırasında bir hata oluştu: " + ex.Message);
            }
        }
        else
            return BadRequest("E-posta gönderimi sırasında bir hata oluştu: Mail bilgileri ayarlanmamış.");
    }

    [HttpPut("AvatarUpdate")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> AvatarUpdate(IFormFile file)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = "/Resources/Uploads/User";

        var command = new UserAvatarUpdate();

        int? userId = UserExtensions.GetCurrentUser();
        command.UserId = userId.Value;
        try
        {
            var client = new FtpClient(ftpServer, username, password, 21);
            client.Connect();

            if (!client.DirectoryExists(baseUrl[1..]))
                client.CreateDirectory(baseUrl);


            if (file is not null && file.Length > 0)
            {

                string newFileName = userId.Value.ToString() + "avatar" + Path.GetExtension(file.FileName);

                string tempFilePath = Path.GetTempFileName();

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                if (client.FileExists($"{baseUrl}/{newFileName}"))
                {
                    client.DeleteFile($"{baseUrl}/{newFileName}");
                }
                using var fileStream = System.IO.File.OpenRead(tempFilePath);
                client.UploadFile(tempFilePath, $"{baseUrl}/{newFileName}");

                command.Avatar = $"{baseUrl}/{newFileName}";
            }


            client.Disconnect();
        }
        catch (Exception)
        {

        }


        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
