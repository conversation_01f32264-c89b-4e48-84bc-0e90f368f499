﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<system.webServer>
		<!--<rewrite>
			<rules>
				<rule name="HTTPS Redirect" stopProcessing="true">
					<match url="(.*)" />
					<conditions logicalGrouping="MatchAll" trackAllCaptures="false">
						<add input="{HTTPS}" pattern="^OFF$" />
					</conditions>
					<action type="Redirect" url="https://{HTTP_HOST}/{R:1}" appendQueryString="false" />
				</rule>
			</rules>
		</rewrite>-->
		<handlers>
			<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			<remove name="WebDAV" />
		</handlers>
		<modules runAllManagedModulesForAllRequests="true">
			<remove name="WebDAVModule" />
		</modules>
		<aspNetCore processPath="dotnet" arguments=".\Crystalaligner.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="1073741824" />
			</requestFiltering>
		</security>
	</system.webServer>
</configuration>