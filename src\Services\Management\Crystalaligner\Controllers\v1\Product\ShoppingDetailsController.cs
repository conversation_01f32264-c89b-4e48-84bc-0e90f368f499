﻿using Crystalaligner.Application.Commands.Products;
using Crystalaligner.Application.Queries.MultipleQuery.Products;
using Crystalaligner.Application.Queries.SingleQuery.Products;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Domain.Entities.Products;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Management.Controllers.v1.Chats;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class ShoppingDetailsController : BaseController
{
    private readonly IMediator _mediatr;
    public ShoppingDetailsController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<ShoppingDetail>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new ShoppingDetailsPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Satis_Detay.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { "Shopping", "Product" };

        Expression<Func<ShoppingDetail, bool>> predicate = s => s.Id > 0;

        var query = new ShoppingDetailsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<ShoppingDetail>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new ShoppingDetailsPagedQuery(request);
        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<ShoppingDetail, bool>> predicate = s => s.Id == id;

        var includes = new string[] { "Shopping", "Product" };

        var query = new ShoppingDetailQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("Increase/{id}")]
    public async Task<IActionResult> Increase(int id)
    {
        var result = await _mediatr.Send(new ShoppingDetailIncrease {Id = id } );

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("Decrease/{id}")]
    public async Task<IActionResult> Decrease(int id)
    {
        var result = await _mediatr.Send(new ShoppingDetailDecrease {Id = id });

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("Remove/{id}")]
    public async Task<IActionResult> Remove(int id)
    {
        var result = await _mediatr.Send(new ShoppingDetailRemove {Id = id });

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByShoppingId/{id}")]
    public async Task<IActionResult> GetByShoppingId(int id)
    {
        Expression<Func<ShoppingDetail, bool>> predicate = s => s.ShoppingId == id;

        var includes = new string[] { "Shopping", "Product" };

        var query = new ShoppingDetailsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] ShoppingDetailCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] ShoppingDetailUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<ShoppingDetail, bool>> predicate = s => s.Id == id;

        var query = new ShoppingDetailDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
}
