﻿using Crystalaligner.Application.Queries.SingleQuery.Dashboards;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.ViewModels.Dashboards;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Crystalaligner.Controllers.v1;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class DashboardController : BaseController
{
    private readonly IMediator _mediatr;
    public DashboardController(IMediator mediatr) => _mediatr = mediatr;


    [HttpGet("GetMobileHomePage")]
    public async Task<IActionResult> GetMobileHomePage(int? status, string name)
    {
        var includes = new string[] { };

        Expression<Func<MobileHomePage, bool>> predicate = s => s.OrderId > 0;


        var query = new MobileHomePageQuery(predicate, includes, status, name);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetMobileRefinementPage")]
    public async Task<IActionResult> GetMobileRefinementPage(int? status, string name)
    {
        var includes = new string[] { };

        Expression<Func<MobileRefinementPage, bool>> predicate = s => s.OrderId > 0;


        var query = new MobileHomeRefinementPageQuery(predicate, includes, status, name);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }
    [HttpGet("GetOrderStatusStatistics")]
    public async Task<IActionResult> GetOrderStatusStatistics()
    {
        var includes = new string[] { };

        Expression<Func<OrderStatusStatistic, bool>> predicate = s => -1 > 0;

        var query = new OrderStatusStatisticQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("CountDoctorOrders")]
    public async Task<IActionResult> CountDoctorOrders()
    {
        var query = new CountDoctorOrdersQuery();

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("CountOfDoctorsByCity")]
    public async Task<IActionResult> CountOfDoctorsByCity()
    {
        var query = new CountOfDoctorsByCityQuery();

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("CountOfOrdersByStatus")]
    public async Task<IActionResult> CountOfOrdersByStatus()
    {
        var query = new CountOfOrdersByStatusQuery();

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("CountAvailableQuantityByPackageType")]
    public async Task<IActionResult> CountAvailableQuantityByPackageType()
    {
        var query = new CountAvailableQuantityByPackageTypeQuery();

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [AllowAnonymous]
    [HttpGet("TestWhatsapp")]
    public async Task<IActionResult> TestWhatsapp()
    {
        HttpClient _httpClient = new HttpClient();

        var phoneNumberId = 512970705228507;
        var accessToken = "EAA23XUu5peQBOzs8tjrbQy5r4zQVlhi5mHb7SZAF2RZABUcPGFiITsPCePYkp5tF9AIFR5LSmx4Ahy5TaTvtu0sXf6SUMHZCZCUHvKfqS8hihiOApvQSgjB0AhH2V7kQ2jOk6k7QtmUbvDBs1UZBDZCBuEcOnWkLs6k4CBOkYpu6NHvFCdOJVeScZALDKhUMZCKm";
        var baseUrl = "https://graph.facebook.com/v21.0/";
        var messageTo = "905383485870";
        var templateName = "ilan_bilgilendirme";

        var apiUrl = $"{baseUrl}{phoneNumberId}/messages";

        var payload = new
        {
            messaging_product = "whatsapp",
            recipient_type = "individual",
            to = messageTo,
            type = "template",
            template = new
            {
                name = templateName,
                language = new { code = "tr" },
                components = new[]
                    {
                        new
                        {
                            type = "body",
                            parameters = new[]
                            {
                                new { type = "text", text = "order.Date.ToString('dd-MM-yyyy hh:mm')" },
                                new { type = "text", text = "order.OrderWelcome.PassengerName" },
                                new { type = "text", text = "order.OrderWelcome.FlightCode" },
                                new { type = "text", text = "order.Name" },
                                new { type = "text", text = "offer.Vehicle.Plate" },
                                new { type = "text", text = "offer.Driver.Name" },
                                new { type = "text", text = "offer.Driver.Phone" }
                            }
                        }
                    }
            }
        };

        var stringContent = new StringContent(JsonSerializer.Serialize(payload), Encoding.UTF8, "application/json");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        var response = await _httpClient.PostAsync(apiUrl, stringContent);
        string resultJson = response.Content.ReadAsStringAsync().Result;  

        return Ok(resultJson);
    }




}

