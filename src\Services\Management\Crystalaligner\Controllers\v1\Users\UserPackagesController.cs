﻿using Crystalaligner.Application.Commands.Users;
using Crystalaligner.Application.Queries.MultipleQuery.Users;
using Crystalaligner.Application.Queries.SingleQuery.Users;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.GenericExpressions;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;
using static Crystalaligner.Helper.Enumerations;

namespace Crystalaligner.Controllers.v1.Users;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class UserPackagesController : BaseController
{
    private readonly IMediator _mediatr;
    public UserPackagesController(IMediator mediatr) => _mediatr = mediatr;

    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<UserPackage>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new UserPackagesPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Kullanici_Paket_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {

        var query = new UserPackagesQuery();

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<UserPackage, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new UserPackageQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPackagesByUserId/{userId}")]
    public async Task<IActionResult> GetPackagesByUserId(int userId)
    {
        GenericExpression<UserPackage> expression = new GenericExpression<UserPackage>();
        expression.predicate = s => s.UserId == userId;

        var query = new UserPackagesQuery(expression);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetAvailablePackagesByPackageId/{PackageId}")]
    public async Task<IActionResult> GetAvailablePackagesByPackageId(int PackageId)
    {
        int? userId = UserExtensions.GetCurrentUser();
        if (userId is null)
            return Unauthorized();
        GenericExpression<UserPackage> expression = new GenericExpression<UserPackage>();
        expression.predicate = s => s.PackageId == PackageId
        && s.UserId == userId.Value 
        && s.AvailableQuantity > 0;

        var query = new UserPackagesQuery(expression);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetAvailablePackagesByPackageTypeId")]
    public async Task<IActionResult> GetAvailablePackagesByPackageTypeId([FromQuery] int PackageTypeId, int ArchType)
    {
        int? userId = userId = 2355; //UserExtensions.GetCurrentUser();
                                     //if (userId is null) 
                                     //return Unauthorized();
        GenericExpression<UserPackage> expression = new GenericExpression<UserPackage>();
        expression.predicate = s =>
        s.Package.PackageTypeId == PackageTypeId
        && s.Package.PackageType.ArkType == (ArkType)ArchType
        && s.UserId == userId.Value
        && s.AvailableQuantity > 0;

        var query = new UserPackagesQuery(expression);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<UserPackage>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new UserPackagesPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] UserPackageCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("AssignPackageToUser")]
    public async Task<IActionResult> AssignPackageToUser([FromBody] UserPackageWithFullDiscountCreate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] UserPackageUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<UserPackage, bool>> predicate = s => s.Id == id;

        var query = new UserPackageDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("UserPackageReset")]
    public async Task<IActionResult> UserPackageReset([FromBody] UserPackageResetUpdate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("UserPackageDecrease")]
    public async Task<IActionResult> UserPackageDecrease([FromBody] UserPackageDecreaseUpdate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        return result.Success ? Success(result) : BadRequest(result);
    }
}
