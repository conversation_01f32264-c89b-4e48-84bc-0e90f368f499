﻿using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Domain.Entities;
using Crystalaligner.Management.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v2
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("2.0")]
    [BasicAuthentication]
    [ApiController]
    public class AdvantageController : BaseController
    {
        private readonly IMediator _mediatr;
        public AdvantageController(IMediator mediatr) => _mediatr = mediatr;
        [HttpGet("GetByLanguageId/{LanguageId}")]
        public async Task<IActionResult> GetByLangugageId(int LanguageId)
        {
            Expression<Func<Advantage, bool>> predicate = s => s.LanguageId == LanguageId;

            var includes = new string[] { };

            var query = new AdvantagesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }

        [HttpGet("AdvantageImage")]
        public async Task<IActionResult> GetAdvantageImage()
        {
            var includes = new string[] { };

            Expression<Func<AdvantageImage, bool>> predicate = s => s.Id > 0;

            var query = new AdvantageImagesQuery(predicate, includes);

            var result = await _mediatr.Send(query);

            if (result == null) return NotFound();

            return result.Success ? Success(result) : BadRequest(result);
        }
    }
}
