﻿using Crystalaligner.Application.Commands.Information;
using Crystalaligner.Application.Queries.MultipleQuery;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Management.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Crystalaligner.Controllers.v1;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class InformationController : BaseController
{
    private readonly IMediator _mediatr;
    private readonly IHostEnvironment _environment;
    private readonly IConfiguration _configuration;
    public InformationController(IMediator mediatr, IHostEnvironment environment, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _environment = environment;
        _configuration = configuration;
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = Array.Empty<string>();

        Expression<Func<Information, bool>> predicate = s => s.Id > 0;

        var query = new InformationQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut]
    public async Task<IActionResult> Put([FromBody] InformationUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetEnvironmentInfo")]
    [AllowAnonymous]
    public async Task<IActionResult> GetEnvironmentInfo()
    {
        var environmentName = _environment.EnvironmentName;

        return Ok($"Environment: {environmentName}");

    }
}
